// Core Types for CashBoost Platform

export interface Store {
  id: string;
  name: string;
  slug: string;
  logo: string;
  logoAlt: string;
  website: string;
  description: string;
  category: StoreCategory;
  featured: boolean;
  verified: boolean;
  trustScore: number; // 1-5 rating
  totalOffers: number;
  lastUpdated: string;
  brandColors: {
    primary: string;
    secondary?: string;
  };
}

export interface CashbackRate {
  id: string;
  storeId: string;
  platform: CashbackPlatform;
  rate: number; // Percentage (e.g., 5.5 for 5.5%)
  rateType: 'percentage' | 'fixed' | 'points';
  currency?: string; // For fixed rates
  pointsPerDollar?: number; // For points-based systems
  conditions?: string[];
  validUntil?: string;
  isPromotional: boolean;
  lastVerified: string;
  trend: 'up' | 'down' | 'stable';
  trendPercentage?: number;
}

export interface CashbackPlatform {
  id: string;
  name: string;
  slug: string;
  logo: string;
  logoAlt: string;
  website: string;
  description: string;
  trustScore: number; // 1-5 rating
  payoutMethods: PayoutMethod[];
  minimumPayout: number;
  payoutCurrency: string;
  averagePayoutTime: string; // e.g., "2-3 business days"
  signupBonus?: number;
  referralBonus?: number;
  features: PlatformFeature[];
  countries: string[]; // ISO country codes
  founded: string;
  totalUsers?: number;
  verified: boolean;
}

export interface PayoutMethod {
  type: 'paypal' | 'bank_transfer' | 'gift_card' | 'check' | 'crypto';
  name: string;
  minimumAmount: number;
  processingTime: string;
  fees?: number;
}

export interface PlatformFeature {
  name: string;
  description: string;
  available: boolean;
}

export interface StoreCategory {
  id: string;
  name: string;
  slug: string;
  icon: string;
  description: string;
  storeCount: number;
  averageCashback: number;
  topStores: string[]; // Store IDs
}

export interface CashbackComparison {
  storeId: string;
  store: Store;
  rates: CashbackRate[];
  bestRate: CashbackRate;
  averageRate: number;
  totalPlatforms: number;
  lastUpdated: string;
}

export interface SearchFilters {
  query?: string;
  categories?: string[];
  platforms?: string[];
  minRate?: number;
  maxRate?: number;
  rateType?: 'percentage' | 'fixed' | 'points';
  featured?: boolean;
  verified?: boolean;
  sortBy?: 'rate' | 'name' | 'popularity' | 'lastUpdated';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResults {
  stores: CashbackComparison[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  filters: SearchFilters;
  suggestions?: string[];
}

export interface TrustIndicator {
  type: 'users' | 'stores' | 'savings' | 'reviews';
  value: number;
  label: string;
  description: string;
  icon: string;
  animated?: boolean;
}

export interface FeaturedOffer {
  id: string;
  storeId: string;
  store: Store;
  title: string;
  description: string;
  cashbackRate: number;
  originalRate?: number; // For promotional offers
  validUntil?: string;
  terms?: string[];
  featured: boolean;
  urgent?: boolean;
  imageUrl?: string;
  ctaText: string;
  ctaUrl: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: string;
  updatedAt: string;
  tags: string[];
  category: string;
  featuredImage: string;
  readingTime: number; // in minutes
  seoTitle?: string;
  seoDescription?: string;
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
  notHelpful: number;
  lastUpdated: string;
}

export interface UserPreferences {
  favoriteStores: string[];
  preferredPlatforms: string[];
  categories: string[];
  notifications: {
    rateChanges: boolean;
    newOffers: boolean;
    weeklyDigest: boolean;
  };
  currency: string;
  country: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    hasMore?: boolean;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors: string[];
  code?: string;
  statusCode: number;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface CashbackCardProps extends BaseComponentProps {
  comparison: CashbackComparison;
  featured?: boolean;
  compact?: boolean;
  showPlatforms?: boolean;
}

export interface SearchBarProps extends BaseComponentProps {
  onSearch: (query: string) => void;
  onFiltersChange: (filters: SearchFilters) => void;
  placeholder?: string;
  showFilters?: boolean;
  initialQuery?: string;
}

export interface ComparisonTableProps extends BaseComponentProps {
  comparisons: CashbackComparison[];
  sortable?: boolean;
  pagination?: boolean;
  loading?: boolean;
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type Theme = 'light' | 'dark' | 'auto';

export type Breakpoint = 'mobile' | 'tablet' | 'desktop';

export type AnimationType = 'fade' | 'slide' | 'scale' | 'bounce';

// Form Types
export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
  newsletter?: boolean;
}

export interface NewsletterForm {
  email: string;
  preferences?: {
    deals: boolean;
    updates: boolean;
    tips: boolean;
  };
}

// Analytics Types
export interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties?: Record<string, any>;
}

export interface PageView {
  page: string;
  title: string;
  referrer?: string;
  timestamp: string;
  userId?: string;
  sessionId: string;
}
