'use client'

import { motion } from 'framer-motion'
import { 
  RocketLaunchIcon,
  SparklesIcon,
  ArrowRightIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'

const quickBenefits = [
  {
    icon: CurrencyDollarIcon,
    text: 'Start earning cashback immediately',
  },
  {
    icon: ClockIcon,
    text: 'Save hours of manual comparison',
  },
  {
    icon: ShieldCheckIcon,
    text: 'Always get the best available rates',
  },
]

const urgencyStats = [
  { value: '847', label: 'Users joined today' },
  { value: '$12,450', label: 'Saved in the last hour' },
  { value: '23', label: 'Rate increases detected' },
]

export default function CTASection() {
  return (
    <section className="py-responsive bg-gradient-to-br from-emerald-600 via-emerald-500 to-green-500 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-40 h-40 bg-white rounded-full blur-3xl" />
        <div className="absolute top-32 right-20 w-32 h-32 bg-white rounded-full blur-2xl" />
        <div className="absolute bottom-20 left-32 w-48 h-48 bg-white rounded-full blur-3xl" />
        <div className="absolute bottom-10 right-10 w-36 h-36 bg-white rounded-full blur-2xl" />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(15)].map((_, index) => (
          <motion.div
            key={index}
            initial={{
              opacity: 0,
              x: Math.random() * 1200,
              y: Math.random() * 800,
            }}
            animate={{
              opacity: [0, 0.3, 0],
              y: [
                Math.random() * 800,
                Math.random() * 800 - 100,
              ],
            }}
            transition={{
              duration: Math.random() * 8 + 6,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: 'linear',
              delay: Math.random() * 5,
            }}
            className="absolute text-white/20 text-xl font-bold"
          >
            {['$', '%', '💰', '⚡', '🎯'][Math.floor(Math.random() * 5)]}
          </motion.div>
        ))}
      </div>

      <div className="container-responsive relative z-10">
        {/* Urgency Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-8 mb-12"
        >
          {urgencyStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-2xl lg:text-3xl font-poppins font-black text-white mb-1">
                {stat.value}
              </div>
              <div className="text-emerald-100 text-sm font-medium">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        <div className="max-w-4xl mx-auto text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm text-white font-medium text-sm mb-8"
          >
            <SparklesIcon className="w-4 h-4 mr-2" />
            Ready to Start Saving?
          </motion.div>

          {/* Main Headline */}
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-responsive-3xl font-poppins font-black text-white mb-6 leading-tight"
          >
            Don't Leave Money{' '}
            <span className="relative">
              <span className="bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-100 bg-clip-text text-transparent">
                on the Table
              </span>
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-300 to-yellow-100 rounded-full"
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 1.2 }}
                viewport={{ once: true }}
              />
            </span>
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="text-responsive-lg text-emerald-100 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            Join over 2 million smart shoppers who use CashBoost to maximize their cashback earnings. 
            Start comparing rates and saving money today.
          </motion.p>

          {/* Benefits List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col md:flex-row justify-center items-center gap-6 mb-12"
          >
            {quickBenefits.map((benefit, index) => (
              <motion.div
                key={benefit.text}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.7 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center space-x-3 text-emerald-100"
              >
                <div className="flex-shrink-0 w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <benefit.icon className="w-4 h-4" />
                </div>
                <span className="font-medium">
                  {benefit.text}
                </span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-emerald-600 font-bold text-lg px-10 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-2 group"
            >
              <RocketLaunchIcon className="w-6 h-6 group-hover:animate-bounce" />
              <span>Start Comparing Now</span>
              <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-transparent text-white border-2 border-white/30 hover:border-white/50 font-semibold text-lg px-10 py-4 rounded-2xl backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
            >
              See How It Works
            </motion.button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <p className="text-emerald-100 text-sm mb-4">
              Trusted by millions • Free forever • No signup required
            </p>
            
            {/* Social Proof */}
            <div className="flex justify-center items-center space-x-6 text-emerald-200">
              <div className="flex items-center space-x-2">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full border-2 border-white flex items-center justify-center text-white text-xs font-bold"
                    >
                      {String.fromCharCode(65 + i - 1)}
                    </div>
                  ))}
                </div>
                <span className="text-sm font-medium">
                  2.1M+ users
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-300 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
                <span className="text-sm font-medium ml-2">
                  4.9/5 rating
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
