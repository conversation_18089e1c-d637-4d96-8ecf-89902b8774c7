'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon,
  StarIcon,
  ArrowTopRightOnSquareIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import CashbackRateCard from './CashbackRateCard'

interface Platform {
  name: string
  rate: number
  trend: 'up' | 'down' | 'stable'
  trendPercent?: number
}

interface Store {
  id: string
  name: string
  logo: string
  category: string
  verified: boolean
  trustScore: number
}

interface CashbackRateData {
  id: string
  store: Store
  bestRate: number
  platforms: Platform[]
  featured: boolean
  lastUpdated: string
}

// Mock data - in real app this would come from API
const topRates: CashbackRateData[] = [
  {
    id: '1',
    store: {
      id: 'nike',
      name: '<PERSON>',
      logo: '/logos/nike.svg',
      category: 'Fashion & Apparel',
      verified: true,
      trustScore: 4.8,
    },
    bestRate: 8.5,
    platforms: [
      { name: 'TopCashback', rate: 8.5, trend: 'up', trendPercent: 15 },
      { name: '<PERSON><PERSON><PERSON>', rate: 7.0, trend: 'stable' },
      { name: '<PERSON>', rate: 6.5, trend: 'down', trendPercent: 5 },
    ],
    featured: true,
    lastUpdated: '2 hours ago',
  },
  {
    id: '2',
    store: {
      id: 'amazon',
      name: 'Amazon',
      logo: '/logos/amazon.svg',
      category: 'Everything Store',
      verified: true,
      trustScore: 4.9,
    },
    bestRate: 5.5,
    platforms: [
      { name: 'Rakuten', rate: 5.5, trend: 'up', trendPercent: 10 },
      { name: 'TopCashback', rate: 4.8, trend: 'stable' },
      { name: 'BeFrugal', rate: 4.2, trend: 'up', trendPercent: 8 },
    ],
    featured: true,
    lastUpdated: '1 hour ago',
  },
  {
    id: '3',
    store: {
      id: 'target',
      name: 'Target',
      logo: '/logos/target.svg',
      category: 'Department Store',
      verified: true,
      trustScore: 4.7,
    },
    bestRate: 4.2,
    platforms: [
      { name: 'BeFrugal', rate: 4.2, trend: 'up', trendPercent: 20 },
      { name: 'TopCashback', rate: 3.8, trend: 'stable' },
      { name: 'Rakuten', rate: 3.5, trend: 'down', trendPercent: 3 },
    ],
    featured: false,
    lastUpdated: '3 hours ago',
  },
  {
    id: '4',
    store: {
      id: 'bestbuy',
      name: 'Best Buy',
      logo: '/logos/bestbuy.svg',
      category: 'Electronics',
      verified: true,
      trustScore: 4.6,
    },
    bestRate: 4.0,
    platforms: [
      { name: 'TopCashback', rate: 4.0, trend: 'stable' },
      { name: 'Rakuten', rate: 3.5, trend: 'up', trendPercent: 12 },
      { name: 'Honey', rate: 3.2, trend: 'stable' },
    ],
    featured: false,
    lastUpdated: '4 hours ago',
  },
  {
    id: '5',
    store: {
      id: 'walmart',
      name: 'Walmart',
      logo: '/logos/walmart.svg',
      category: 'Department Store',
      verified: true,
      trustScore: 4.5,
    },
    bestRate: 3.8,
    platforms: [
      { name: 'TopCashback', rate: 3.8, trend: 'up', trendPercent: 18 },
      { name: 'BeFrugal', rate: 3.2, trend: 'stable' },
      { name: 'Rakuten', rate: 2.8, trend: 'down', trendPercent: 7 },
    ],
    featured: false,
    lastUpdated: '2 hours ago',
  },
  {
    id: '6',
    store: {
      id: 'macys',
      name: "Macy's",
      logo: '/logos/macys.svg',
      category: 'Fashion & Apparel',
      verified: true,
      trustScore: 4.4,
    },
    bestRate: 6.2,
    platforms: [
      { name: 'Rakuten', rate: 6.2, trend: 'up', trendPercent: 25 },
      { name: 'TopCashback', rate: 5.8, trend: 'stable' },
      { name: 'BeFrugal', rate: 5.5, trend: 'up', trendPercent: 10 },
    ],
    featured: false,
    lastUpdated: '1 hour ago',
  },
]

const categories = ['All', 'Fashion & Apparel', 'Electronics', 'Department Store', 'Everything Store']

export default function TopRatesSection() {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [sortBy, setSortBy] = useState('rate') // 'rate', 'name', 'updated'

  const filteredRates = topRates
    .filter(rate => selectedCategory === 'All' || rate.store.category === selectedCategory)
    .sort((a, b) => {
      switch (sortBy) {
        case 'rate':
          return b.bestRate - a.bestRate
        case 'name':
          return a.store.name.localeCompare(b.store.name)
        case 'updated':
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
        default:
          return 0
      }
    })

  const getTrendIcon = (trend: string, trendPercent?: number) => {
    switch (trend) {
      case 'up':
        return <ArrowUpIcon className="w-4 h-4 text-emerald-500" />
      case 'down':
        return <ArrowDownIcon className="w-4 h-4 text-red-500" />
      default:
        return <MinusIcon className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <section id="rates" className="py-responsive bg-white">
      <div className="container-responsive">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6"
          >
            <ArrowUpIcon className="w-4 h-4 mr-2" />
            Live Cashback Rates
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
          >
            Top Cashback Rates{' '}
            <span className="gradient-text">Right Now</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
          >
            Real-time comparison of cashback rates across all major platforms. 
            Updated every hour to ensure you never miss the best deals.
          </motion.p>
        </div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-12"
        >
          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-xl font-medium text-sm transition-all duration-200 ${
                  selectedCategory === category
                    ? 'bg-emerald-500 text-white shadow-green-soft'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Sort Options */}
          <div className="flex items-center gap-4">
            <FunnelIcon className="w-5 h-5 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 rounded-xl border border-gray-200 text-sm font-medium text-gray-700 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-100 outline-none"
            >
              <option value="rate">Highest Rate</option>
              <option value="name">Store Name</option>
              <option value="updated">Recently Updated</option>
            </select>
          </div>
        </motion.div>

        {/* Cashback Rate Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredRates.map((rate, index) => (
            <motion.div
              key={rate.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <CashbackRateCard
                rate={rate}
                featured={rate.featured}
                getTrendIcon={getTrendIcon}
              />
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <button className="btn-primary text-lg px-8 py-4">
            View All 500+ Stores
            <ArrowTopRightOnSquareIcon className="w-5 h-5 ml-2" />
          </button>
        </motion.div>
      </div>
    </section>
  )
}
