'use client'

import { motion } from 'framer-motion'
import {
  ShieldCheckIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/solid'

interface Platform {
  name: string
  rate: number
  trend: 'up' | 'down' | 'stable'
  trendPercent?: number
}

interface Store {
  id: string
  name: string
  logo: string
  category: string
  verified: boolean
  trustScore: number
}

interface CashbackRate {
  id: string
  store: Store
  bestRate: number
  platforms: Platform[]
  featured: boolean
  lastUpdated: string
}

interface CashbackRateCardProps {
  rate: CashbackRate
  featured?: boolean
  getTrendIcon: (trend: string, trendPercent?: number) => React.ReactNode
}

export default function CashbackRateCard({ 
  rate, 
  featured = false, 
  getTrendIcon 
}: CashbackRateCardProps) {

  return (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className={`${
        featured ? 'card-featured' : 'card'
      } p-6 relative overflow-hidden group cursor-pointer`}
    >
      {/* Featured Badge */}
      {featured && (
        <div className="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
          FEATURED
        </div>
      )}

      {/* Store Header */}
      <div className="flex items-center space-x-3 mb-4">
        {/* Store Logo Placeholder */}
        <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center shadow-sm">
          <span className="text-gray-600 font-bold text-lg">
            {rate.store.name.charAt(0)}
          </span>
        </div>

        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-poppins font-bold text-base text-gray-900">
              {rate.store.name}
            </h3>
            {rate.store.verified && (
              <ShieldCheckIcon className="w-4 h-4 text-emerald-500" />
            )}
          </div>

          <p className="text-xs text-gray-500">
            {rate.store.category}
          </p>
        </div>
      </div>

      {/* Best Rate Display */}
      <div className="text-center mb-4">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 shadow-md mb-2">
          <span className="text-xl font-poppins font-black text-white">
            {rate.bestRate}%
          </span>
        </div>
        <p className="text-xs text-gray-600 font-medium">
          Best Rate
        </p>
      </div>

      {/* Platform Comparison */}
      <div className="space-y-2 mb-4">
        <h4 className="text-xs font-semibold text-gray-600 mb-2">
          Top Platforms:
        </h4>
        {rate.platforms.slice(0, 2).map((platform) => (
          <div key={platform.name} className="flex items-center justify-between">
            <span className="text-sm text-gray-700">
              {platform.name}
            </span>
            <div className="flex items-center space-x-1">
              <span className="font-bold text-emerald-600 text-sm">
                {platform.rate}%
              </span>
              {getTrendIcon(platform.trend, platform.trendPercent)}
            </div>
          </div>
        ))}
      </div>

      {/* Last Updated */}
      <div className="text-center text-xs text-gray-500 mb-3">
        <span>Updated {rate.lastUpdated} • {rate.platforms.length} platforms</span>
      </div>

      {/* Action Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full bg-emerald-500 hover:bg-emerald-600 text-white font-semibold py-2.5 px-4 rounded-xl transition-all duration-200 text-sm"
      >
        View All Rates
        <ArrowTopRightOnSquareIcon className="w-4 h-4 ml-1 inline" />
      </motion.button>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none rounded-2xl" />
    </motion.div>
  )
}
