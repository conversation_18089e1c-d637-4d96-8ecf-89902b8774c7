'use client'

import { motion } from 'framer-motion'
import { 
  StarIcon as StarIconSolid,
  ShieldCheckIcon,
  ClockIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/solid'
import { StarIcon } from '@heroicons/react/24/outline'

interface Platform {
  name: string
  rate: number
  trend: 'up' | 'down' | 'stable'
  trendPercent?: number
}

interface Store {
  id: string
  name: string
  logo: string
  category: string
  verified: boolean
  trustScore: number
}

interface CashbackRate {
  id: string
  store: Store
  bestRate: number
  platforms: Platform[]
  featured: boolean
  lastUpdated: string
}

interface CashbackRateCardProps {
  rate: CashbackRate
  featured?: boolean
  getTrendIcon: (trend: string, trendPercent?: number) => React.ReactNode
}

export default function CashbackRateCard({ 
  rate, 
  featured = false, 
  getTrendIcon 
}: CashbackRateCardProps) {
  const renderStars = (score: number) => {
    const stars = []
    const fullStars = Math.floor(score)
    const hasHalfStar = score % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <StarIconSolid key={i} className="w-4 h-4 text-yellow-400" />
      )
    }

    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative">
          <StarIcon className="w-4 h-4 text-gray-300" />
          <div className="absolute inset-0 overflow-hidden w-1/2">
            <StarIconSolid className="w-4 h-4 text-yellow-400" />
          </div>
        </div>
      )
    }

    const remainingStars = 5 - Math.ceil(score)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <StarIcon key={`empty-${i}`} className="w-4 h-4 text-gray-300" />
      )
    }

    return stars
  }

  return (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className={`${
        featured ? 'card-featured' : 'card'
      } p-6 relative overflow-hidden group cursor-pointer`}
    >
      {/* Featured Badge */}
      {featured && (
        <div className="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
          FEATURED
        </div>
      )}

      {/* Store Header */}
      <div className="flex items-center space-x-4 mb-6">
        {/* Store Logo Placeholder */}
        <div className="w-16 h-16 rounded-2xl bg-gray-100 flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-200">
          <div className="w-10 h-10 bg-gray-300 rounded-lg flex items-center justify-center">
            <span className="text-gray-600 font-bold text-sm">
              {rate.store.name.charAt(0)}
            </span>
          </div>
        </div>

        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-poppins font-bold text-lg text-gray-900">
              {rate.store.name}
            </h3>
            {rate.store.verified && (
              <ShieldCheckIcon className="w-5 h-5 text-emerald-500" />
            )}
          </div>
          
          <p className="text-sm text-gray-500 mb-2">
            {rate.store.category}
          </p>

          {/* Trust Score */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              {renderStars(rate.store.trustScore)}
            </div>
            <span className="text-sm text-gray-600 font-medium">
              {rate.store.trustScore}
            </span>
          </div>
        </div>
      </div>

      {/* Best Rate Display */}
      <div className="text-center mb-6">
        <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 shadow-green-medium mb-3 group-hover:shadow-green-strong transition-shadow duration-200">
          <span className="text-2xl font-poppins font-black text-white">
            {rate.bestRate}%
          </span>
        </div>
        <p className="text-sm text-gray-600 font-medium">
          Best Cashback Rate
        </p>
      </div>

      {/* Platform Comparison */}
      <div className="space-y-3 mb-6">
        <h4 className="text-sm font-semibold text-gray-700 mb-3">
          Platform Comparison:
        </h4>
        {rate.platforms.slice(0, 3).map((platform, index) => (
          <div key={platform.name} className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">
                {platform.name}
              </span>
              {getTrendIcon(platform.trend, platform.trendPercent)}
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-bold text-emerald-600">
                {platform.rate}%
              </span>
              {platform.trendPercent && (
                <span className={`text-xs font-medium ${
                  platform.trend === 'up' ? 'text-emerald-500' : 'text-red-500'
                }`}>
                  {platform.trend === 'up' ? '+' : '-'}{platform.trendPercent}%
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Last Updated */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
        <div className="flex items-center space-x-1">
          <ClockIcon className="w-3 h-3" />
          <span>Updated {rate.lastUpdated}</span>
        </div>
        <span className="font-medium">
          {rate.platforms.length} platforms
        </span>
      </div>

      {/* Action Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full btn-primary justify-center group-hover:shadow-green-strong transition-all duration-200"
      >
        Compare All Rates
        <ArrowTopRightOnSquareIcon className="w-4 h-4 ml-2" />
      </motion.button>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none rounded-2xl" />
    </motion.div>
  )
}
