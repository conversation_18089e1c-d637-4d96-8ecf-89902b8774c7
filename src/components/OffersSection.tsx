'use client'

import { motion } from 'framer-motion'
import { 
  FireIcon,
  ClockIcon,
  TagIcon,
  ArrowTopRightOnSquareIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

const featuredOffers = [
  {
    id: '1',
    store: 'Nike',
    title: 'Holiday Sale Boost',
    description: 'Extra 3% cashback on top of regular rates during holiday season',
    cashbackRate: 11.5,
    originalRate: 8.5,
    platform: 'TopCashback',
    validUntil: '2024-01-15',
    urgent: true,
    category: 'Fashion & Apparel',
    featured: true,
    terms: ['Valid on full-price items only', 'Cannot be combined with other offers'],
    ctaText: 'Get 11.5% Cashback',
  },
  {
    id: '2',
    store: 'Amazon',
    title: 'Prime Day Special',
    description: 'Increased cashback rates for Prime members during exclusive deals',
    cashbackRate: 8.0,
    originalRate: 5.5,
    platform: 'Rakuten',
    validUntil: '2024-01-10',
    urgent: false,
    category: 'Everything Store',
    featured: true,
    terms: ['Prime membership required', 'Valid on eligible categories'],
    ctaText: 'Shop with 8% Back',
  },
  {
    id: '3',
    store: 'Target',
    title: 'New Year Savings',
    description: 'Start the year right with boosted cashback on home essentials',
    cashbackRate: 6.5,
    originalRate: 4.2,
    platform: 'BeFrugal',
    validUntil: '2024-01-31',
    urgent: false,
    category: 'Department Store',
    featured: false,
    terms: ['Home & Garden category only', 'Minimum $50 purchase'],
    ctaText: 'Earn 6.5% Back',
  },
  {
    id: '4',
    store: 'Best Buy',
    title: 'Tech Upgrade Bonus',
    description: 'Higher cashback on electronics and tech accessories',
    cashbackRate: 7.2,
    originalRate: 4.0,
    platform: 'TopCashback',
    validUntil: '2024-01-20',
    urgent: true,
    category: 'Electronics',
    featured: false,
    terms: ['Electronics category only', 'Excludes gaming consoles'],
    ctaText: 'Get Tech Cashback',
  },
]

const categories = ['All', 'Fashion & Apparel', 'Electronics', 'Department Store', 'Everything Store']

export default function OffersSection() {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getDaysRemaining = (dateString: string) => {
    const today = new Date()
    const endDate = new Date(dateString)
    const diffTime = endDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <section id="offers" className="py-responsive bg-white">
      <div className="container-responsive">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-orange-50 text-orange-700 font-medium text-sm mb-6"
          >
            <FireIcon className="w-4 h-4 mr-2" />
            Limited Time Offers
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
          >
            Exclusive{' '}
            <span className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">
              Cashback Boosts
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
          >
            Don't miss these time-sensitive offers! Get extra cashback on top of regular rates 
            from your favorite stores.
          </motion.p>
        </div>

        {/* Featured Offers Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {featuredOffers.map((offer, index) => (
            <motion.div
              key={offer.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative overflow-hidden rounded-3xl ${
                offer.featured 
                  ? 'bg-gradient-to-br from-emerald-500 to-emerald-600 text-white' 
                  : 'bg-white border-2 border-gray-100 hover:border-emerald-200'
              } p-8 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer`}
            >
              {/* Urgent Badge */}
              {offer.urgent && (
                <div className="absolute top-4 right-4 bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full animate-pulse">
                  URGENT
                </div>
              )}

              {/* Store & Platform */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className={`text-2xl font-poppins font-bold mb-1 ${
                    offer.featured ? 'text-white' : 'text-gray-900'
                  }`}>
                    {offer.store}
                  </h3>
                  <p className={`text-sm font-medium ${
                    offer.featured ? 'text-emerald-100' : 'text-gray-500'
                  }`}>
                    via {offer.platform}
                  </p>
                </div>
                
                {/* Store Logo Placeholder */}
                <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                  offer.featured ? 'bg-white/20' : 'bg-gray-100'
                }`}>
                  <span className={`font-bold ${
                    offer.featured ? 'text-white' : 'text-gray-600'
                  }`}>
                    {offer.store.charAt(0)}
                  </span>
                </div>
              </div>

              {/* Offer Title & Description */}
              <div className="mb-6">
                <h4 className={`text-xl font-bold mb-2 ${
                  offer.featured ? 'text-white' : 'text-gray-900'
                }`}>
                  {offer.title}
                </h4>
                <p className={`${
                  offer.featured ? 'text-emerald-100' : 'text-gray-600'
                } leading-relaxed`}>
                  {offer.description}
                </p>
              </div>

              {/* Cashback Rate Display */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="text-center">
                  <div className={`text-4xl font-poppins font-black mb-1 ${
                    offer.featured ? 'text-white' : 'text-emerald-600'
                  }`}>
                    {offer.cashbackRate}%
                  </div>
                  <div className={`text-sm font-medium ${
                    offer.featured ? 'text-emerald-100' : 'text-gray-500'
                  }`}>
                    Boosted Rate
                  </div>
                </div>
                
                <div className="flex items-center">
                  <svg className={`w-6 h-6 ${
                    offer.featured ? 'text-emerald-200' : 'text-emerald-500'
                  }`} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>

                <div className="text-center">
                  <div className={`text-2xl font-poppins font-bold mb-1 line-through opacity-60 ${
                    offer.featured ? 'text-emerald-200' : 'text-gray-400'
                  }`}>
                    {offer.originalRate}%
                  </div>
                  <div className={`text-sm font-medium ${
                    offer.featured ? 'text-emerald-100' : 'text-gray-500'
                  }`}>
                    Regular Rate
                  </div>
                </div>
              </div>

              {/* Time Remaining */}
              <div className={`flex items-center space-x-2 mb-6 ${
                offer.featured ? 'text-emerald-100' : 'text-gray-600'
              }`}>
                <ClockIcon className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {getDaysRemaining(offer.validUntil)} days remaining
                </span>
                <span className="text-xs opacity-75">
                  (ends {formatDate(offer.validUntil)})
                </span>
              </div>

              {/* Terms */}
              <div className="mb-6">
                <div className={`text-xs font-medium mb-2 ${
                  offer.featured ? 'text-emerald-100' : 'text-gray-500'
                }`}>
                  Terms & Conditions:
                </div>
                <ul className="space-y-1">
                  {offer.terms.map((term, termIndex) => (
                    <li key={termIndex} className={`text-xs flex items-start space-x-2 ${
                      offer.featured ? 'text-emerald-100' : 'text-gray-500'
                    }`}>
                      <span className="mt-1">•</span>
                      <span>{term}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* CTA Button */}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
                  offer.featured
                    ? 'bg-white text-emerald-600 hover:bg-emerald-50'
                    : 'bg-emerald-500 text-white hover:bg-emerald-600'
                }`}
              >
                <span>{offer.ctaText}</span>
                <ArrowTopRightOnSquareIcon className="w-4 h-4" />
              </motion.button>

              {/* Background Pattern */}
              <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                <TagIcon className="w-full h-full" />
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Offers */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <button className="btn-secondary text-lg px-8 py-4">
            View All Special Offers
            <ArrowTopRightOnSquareIcon className="w-5 h-5 ml-2" />
          </button>
        </motion.div>
      </div>
    </section>
  )
}
