'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

const floatingCards = [
  {
    id: 1,
    store: 'Amazon',
    logo: '/logos/amazon.svg',
    rate: '5.5%',
    platform: 'Rakuten',
    position: { top: '15%', left: '10%' },
    delay: 0,
    color: '#FF9900',
  },
  {
    id: 2,
    store: 'Nike',
    logo: '/logos/nike.svg',
    rate: '8.0%',
    platform: 'Honey',
    position: { top: '25%', right: '15%' },
    delay: 0.5,
    color: '#000000',
  },
  {
    id: 3,
    store: 'Target',
    logo: '/logos/target.svg',
    rate: '3.5%',
    platform: 'TopCashback',
    position: { bottom: '30%', left: '8%' },
    delay: 1,
    color: '#CC0000',
  },
  {
    id: 4,
    store: 'Best Buy',
    logo: '/logos/bestbuy.svg',
    rate: '4.2%',
    platform: 'Cashback Monitor',
    position: { bottom: '20%', right: '12%' },
    delay: 1.5,
    color: '#0046BE',
  },
  {
    id: 5,
    store: 'Walmart',
    logo: '/logos/walmart.svg',
    rate: '2.8%',
    platform: 'BeFrugal',
    position: { top: '45%', left: '5%' },
    delay: 2,
    color: '#004C91',
  },
  {
    id: 6,
    store: 'Apple',
    logo: '/logos/apple.svg',
    rate: '1.5%',
    platform: 'Rakuten',
    position: { top: '35%', right: '8%' },
    delay: 2.5,
    color: '#000000',
  },
]

export default function FloatingCashbackCards() {
  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {floatingCards.map((card) => (
        <motion.div
          key={card.id}
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ 
            opacity: [0, 1, 1, 0.7],
            scale: [0.8, 1, 1, 0.9],
            y: [50, 0, -10, 0],
          }}
          transition={{
            duration: 4,
            delay: card.delay,
            repeat: Infinity,
            repeatType: 'reverse',
            ease: 'easeInOut',
          }}
          className="absolute hidden lg:block"
          style={card.position}
        >
          <div className="card-gradient p-4 rounded-2xl shadow-green-soft border border-white/20 backdrop-blur-sm min-w-[200px]">
            {/* Store Header */}
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm"
                style={{ backgroundColor: `${card.color}15` }}
              >
                {/* Store logo representation */}
                <div
                  className="w-6 h-6 rounded flex items-center justify-center text-white font-bold text-xs"
                  style={{ backgroundColor: card.color }}
                >
                  {card.store.charAt(0)}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 text-sm">
                  {card.store}
                </h3>
                <p className="text-xs text-gray-500">
                  via {card.platform}
                </p>
              </div>
            </div>

            {/* Cashback Rate */}
            <div className="text-center">
              <div className="text-2xl font-poppins font-bold text-emerald-600 mb-1">
                {card.rate}
              </div>
              <div className="text-xs text-gray-500 font-medium">
                Cashback Rate
              </div>
            </div>

            {/* Trend Indicator */}
            <div className="flex items-center justify-center mt-2">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="flex items-center space-x-1 text-xs text-emerald-600"
              >
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">Trending</span>
              </motion.div>
            </div>
          </div>
        </motion.div>
      ))}

      {/* Mobile Floating Elements - Simplified */}
      <div className="lg:hidden">
        {[1, 2, 3].map((index) => (
          <motion.div
            key={`mobile-${index}`}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ 
              opacity: [0, 0.6, 0.6, 0.3],
              scale: [0.5, 0.8, 0.8, 0.6],
              y: [30, 0, -5, 0],
            }}
            transition={{
              duration: 3,
              delay: index * 0.5,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: 'easeInOut',
            }}
            className="absolute"
            style={{
              top: `${20 + index * 25}%`,
              [index % 2 === 0 ? 'left' : 'right']: '5%',
            }}
          >
            <div className="glass-green p-3 rounded-xl shadow-green-soft">
              <div className="text-lg font-bold text-emerald-600">
                {index === 1 ? '5.5%' : index === 2 ? '8.0%' : '3.5%'}
              </div>
              <div className="text-xs text-emerald-700">
                Cashback
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Animated Background Particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, index) => (
          <motion.div
            key={`particle-${index}`}
            initial={{
              opacity: 0,
              x: Math.random() * 1200,
              y: Math.random() * 800,
            }}
            animate={{
              opacity: [0, 0.3, 0],
              x: Math.random() * 1200,
              y: Math.random() * 800,
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: 'linear',
              delay: Math.random() * 5,
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
          />
        ))}
      </div>

      {/* Dollar Sign Animations */}
      {[1, 2, 3, 4, 5].map((index) => (
        <motion.div
          key={`dollar-${index}`}
          initial={{
            opacity: 0,
            y: 100,
            x: Math.random() * 1200,
          }}
          animate={{
            opacity: [0, 0.4, 0],
            y: -100,
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            delay: index * 2,
            ease: 'linear',
          }}
          className="absolute text-white/20 text-2xl font-bold pointer-events-none"
        >
          $
        </motion.div>
      ))}
    </div>
  )
}
