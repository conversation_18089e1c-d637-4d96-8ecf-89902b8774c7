'use client'

import { motion } from 'framer-motion'
import { 
  ShieldCheckIcon,
  UsersIcon,
  CurrencyDollarIcon,
  StarIcon,
  HeartIcon,
  TrophyIcon
} from '@heroicons/react/24/outline'

const trustStats = [
  {
    id: 1,
    value: '2.1M+',
    label: 'Happy Users',
    description: 'Shoppers trust CashBoost for their cashback needs',
    icon: UsersIcon,
    color: 'from-blue-500 to-cyan-500',
  },
  {
    id: 2,
    value: '$52M+',
    label: 'Total Savings',
    description: 'Cashback earned by our community members',
    icon: CurrencyDollarIcon,
    color: 'from-emerald-500 to-green-500',
  },
  {
    id: 3,
    value: '500+',
    label: 'Partner Stores',
    description: 'Top brands and retailers in our network',
    icon: ShieldCheckIcon,
    color: 'from-purple-500 to-indigo-500',
  },
  {
    id: 4,
    value: '4.9/5',
    label: 'User Rating',
    description: 'Average rating from verified users',
    icon: StarIcon,
    color: 'from-yellow-500 to-orange-500',
  },
]

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Frequent Online Shopper',
    avatar: '/avatars/sarah.jpg',
    rating: 5,
    text: "CashBoost has completely changed how I shop online. I've saved over $800 this year just by comparing rates before making purchases. The interface is so clean and easy to use!",
    savings: '$847',
    timeUsing: '8 months',
  },
  {
    id: 2,
    name: 'Mike Chen',
    role: 'Tech Enthusiast',
    avatar: '/avatars/mike.jpg',
    rating: 5,
    text: "As someone who buys a lot of electronics, finding the best cashback rates was always a hassle. CashBoost makes it effortless. I wish I had found this platform sooner!",
    savings: '$1,240',
    timeUsing: '1 year',
  },
  {
    id: 3,
    name: 'Emily Rodriguez',
    role: 'Fashion Blogger',
    avatar: '/avatars/emily.jpg',
    rating: 5,
    text: "The real-time rate updates are incredible. I caught a 12% cashback rate on Nike that lasted only a few hours. CashBoost's alerts saved me from missing out on amazing deals.",
    savings: '$623',
    timeUsing: '6 months',
  },
]

const features = [
  {
    title: 'Real-Time Updates',
    description: 'Cashback rates updated every hour',
    icon: '⚡',
  },
  {
    title: 'No Hidden Fees',
    description: 'Completely free to use, always',
    icon: '💯',
  },
  {
    title: 'Verified Rates',
    description: 'All rates verified and accurate',
    icon: '✅',
  },
  {
    title: 'Privacy First',
    description: 'Your data stays private and secure',
    icon: '🔒',
  },
]

export default function TrustSection() {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <section className="py-responsive bg-white">
      <div className="container-responsive">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6"
          >
            <HeartIcon className="w-4 h-4 mr-2" />
            Trusted by Millions
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
          >
            Join the{' '}
            <span className="gradient-text">Smart Shoppers</span>
            {' '}Community
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
          >
            Thousands of shoppers have already discovered the power of smart cashback comparison. 
            See what they're saying about CashBoost.
          </motion.p>
        </div>

        {/* Trust Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {trustStats.map((stat, index) => (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ duration: 0.2 }}
                className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${stat.color} shadow-lg mb-4 group-hover:shadow-xl transition-shadow duration-300`}
              >
                <stat.icon className="w-8 h-8 text-white" />
              </motion.div>
              
              <motion.div
                initial={{ scale: 0.8 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
                viewport={{ once: true }}
                className="text-3xl lg:text-4xl font-poppins font-black text-gray-900 mb-2"
              >
                {stat.value}
              </motion.div>
              
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                {stat.label}
              </h3>
              
              <p className="text-sm text-gray-600 leading-relaxed">
                {stat.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h3 className="text-2xl font-poppins font-bold text-gray-900 mb-4">
              What Our Users Say
            </h3>
            <p className="text-gray-600">
              Real stories from real users who are saving more with CashBoost
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                {/* User Info */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center shadow-md">
                    <span className="text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {testimonial.role}
                    </p>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-1 mb-4">
                  {renderStars(testimonial.rating)}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-gray-700 leading-relaxed mb-6 italic">
                  "{testimonial.text}"
                </blockquote>

                {/* Stats */}
                <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-lg font-bold text-emerald-600">
                      {testimonial.savings}
                    </div>
                    <div className="text-xs text-gray-500">
                      Total Saved
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {testimonial.timeUsing}
                    </div>
                    <div className="text-xs text-gray-500">
                      Using CashBoost
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8 lg:p-12"
        >
          <div className="text-center mb-8">
            <TrophyIcon className="w-12 h-12 text-emerald-600 mx-auto mb-4" />
            <h3 className="text-2xl font-poppins font-bold text-gray-900 mb-4">
              Why CashBoost Stands Out
            </h3>
            <p className="text-gray-600">
              We're committed to providing the best cashback comparison experience
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl hover:bg-white/80 transition-all duration-200"
              >
                <div className="text-3xl mb-3">
                  {feature.icon}
                </div>
                <h4 className="font-bold text-gray-900 mb-2">
                  {feature.title}
                </h4>
                <p className="text-sm text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
