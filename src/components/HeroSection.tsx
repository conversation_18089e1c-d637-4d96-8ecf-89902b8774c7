'use client'

import { useState } from 'react'
import { MagnifyingGlassIcon, SparklesIcon, ShieldCheckIcon } from '@heroicons/react/24/outline'
import { motion } from 'framer-motion'
import FloatingCashbackCards from './FloatingCashbackCards'

const trustStats = [
  { value: '2M+', label: 'Active Users', icon: SparklesIcon },
  { value: '500+', label: 'Partner Stores', icon: ShieldCheckIcon },
  { value: '$50M+', label: 'Cashback Earned', icon: SparklesIcon },
]

export default function HeroSection() {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle search logic here
    console.log('Searching for:', searchQuery)
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
      {/* Background Gradient */}
      <div className="absolute inset-0 hero-gradient" />
      
      {/* Geometric Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg" />
        <div className="absolute bottom-40 left-20 w-40 h-40 bg-white rounded-full blur-2xl" />
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl" />
      </div>

      {/* Floating Cashback Cards */}
      <FloatingCashbackCards />

      {/* Main Content */}
      <div className="relative z-10 container-responsive text-center">
        <div className="max-w-4xl mx-auto">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 rounded-full glass-green text-emerald-700 font-medium text-sm mb-8"
          >
            <SparklesIcon className="w-4 h-4 mr-2" />
            Compare. Save. Earn More.
          </motion.div>

          {/* Main Headline */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-responsive-3xl font-poppins font-black text-white mb-6 leading-tight"
          >
            Find the{' '}
            <span className="relative">
              <span className="bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-100 bg-clip-text text-transparent">
                Best Cashback Rates
              </span>
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-300 to-yellow-100 rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 1 }}
              />
            </span>
            {' '}Across All Platforms
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-responsive-lg text-emerald-50 mb-12 max-w-2xl mx-auto leading-relaxed"
          >
            Compare live cashback rates instantly. No signup required. See which platform offers the best rates for your favorite stores right now.
          </motion.p>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="max-w-2xl mx-auto mb-12"
          >
            <form onSubmit={handleSearch} className="relative">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search for stores like Amazon, Nike, Target..."
                  className="w-full pl-12 pr-40 py-4 text-lg rounded-2xl border-0 shadow-green-strong focus:ring-4 focus:ring-white/20 focus:shadow-green-strong bg-white/95 backdrop-blur-sm placeholder-gray-500 text-gray-900 outline-none transition-all duration-300"
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-emerald-500 hover:bg-emerald-600 text-white font-semibold px-6 py-2.5 rounded-xl transition-all duration-200 shadow-md hover:shadow-lg"
                >
                  Compare Rates
                </button>
              </div>
            </form>
            
            {/* Popular Searches */}
            <div className="mt-6 bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="flex flex-wrap justify-center items-center gap-3">
                <span className="text-white font-medium text-sm">Popular stores:</span>
                {['Amazon', 'Nike', 'Target', 'Best Buy', 'Walmart'].map((store) => (
                  <button
                    key={store}
                    onClick={() => setSearchQuery(store)}
                    className="px-4 py-2 text-sm text-white bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/50 rounded-full transition-all duration-200 focus-visible font-medium"
                  >
                    {store}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Trust Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
          >
            {trustStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-emerald-100 mb-4">
                  <stat.icon className="w-6 h-6 text-emerald-600" />
                </div>
                <div className="text-2xl md:text-3xl font-poppins font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 text-sm font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mt-12"
          >
            <button className="btn-primary text-lg px-8 py-4 animate-pulse-green">
              See Live Rates Below ↓
            </button>
            <button className="btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-8 py-4">
              Compare All Platforms
            </button>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-white/60 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  )
}
