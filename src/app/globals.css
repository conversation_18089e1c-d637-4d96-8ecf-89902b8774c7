@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Green Colors */
    --color-primary: 16 185 129; /* #10B981 - Emerald-500 */
    --color-primary-dark: 5 150 105; /* #059669 - Emerald-600 */
    --color-primary-light: 52 211 153; /* #34D399 - Emerald-400 */
    --color-primary-emerald: 4 120 87; /* #047857 - Emerald-700 */
    
    /* Secondary Colors */
    --color-secondary-green: 46 213 115; /* #2ED573 */
    --color-accent-orange: 245 158 11; /* #F59E0B */
    --color-accent-gold: 252 211 77; /* #FCD34D */
    --color-accent-coral: 255 107 107; /* #FF6B6B */
    
    /* Supporting Colors */
    --color-purple: 139 92 246; /* #8B5CF6 */
    --color-blue: 59 130 246; /* #3B82F6 */
    --color-cyan: 6 182 212; /* #06B6D4 */
    --color-red: 239 68 68; /* #EF4444 */
    
    /* Background Colors */
    --color-bg-primary: 240 253 244; /* #F0FDF4 */
    --color-bg-secondary: 236 253 245; /* #ECFDF5 */
    --color-bg-tertiary: 240 253 250; /* #F0FDFA */
    --color-bg-accent: 254 243 199; /* #FEF3C7 */
    
    /* Text Colors */
    --color-text-primary: 31 41 55; /* #1F2937 */
    --color-text-secondary: 75 85 99; /* #4B5563 */
    --color-text-light: 107 114 128; /* #6B7280 */
    --color-text-ultra-light: 156 163 175; /* #9CA3AF */
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgb(var(--color-bg-primary));
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(var(--color-primary));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--color-primary-dark));
  }
}

@layer components {
  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-emerald-600 via-emerald-500 to-emerald-400 bg-clip-text text-transparent;
  }

  /* Hero Gradient Background */
  .hero-gradient {
    background: linear-gradient(135deg, 
      rgb(var(--color-primary)) 0%, 
      rgb(var(--color-primary-light)) 50%, 
      rgb(110 231 183) 100%
    );
  }

  /* Card Gradient */
  .card-gradient {
    background: linear-gradient(145deg, 
      rgba(255, 255, 255, 0.9) 0%, 
      rgba(240, 253, 244, 0.8) 100%
    );
  }

  /* Button Gradient */
  .btn-gradient {
    background: linear-gradient(135deg, 
      rgb(var(--color-primary)) 0%, 
      rgb(var(--color-primary-dark)) 100%
    );
  }

  /* Glass Effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* Green Glass Effect */
  .glass-green {
    background: rgba(var(--color-primary), 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--color-primary), 0.2);
  }

  /* Shadows */
  .shadow-green-soft {
    box-shadow: 0 4px 20px rgba(var(--color-primary), 0.15);
  }

  .shadow-green-medium {
    box-shadow: 0 8px 30px rgba(var(--color-primary), 0.2);
  }

  .shadow-green-strong {
    box-shadow: 0 15px 40px rgba(var(--color-primary), 0.25);
  }

  .shadow-card {
    box-shadow: 0 10px 25px rgba(var(--color-text-primary), 0.08);
  }

  /* Button Styles */
  .btn-primary {
    @apply btn-gradient text-white font-semibold py-3 px-6 rounded-xl shadow-green-medium hover:shadow-green-strong transform hover:-translate-y-0.5 transition-all duration-300 ease-out;
  }

  .btn-secondary {
    @apply bg-white text-emerald-600 font-semibold py-3 px-6 rounded-xl border-2 border-emerald-200 hover:border-emerald-300 hover:bg-emerald-50 transform hover:-translate-y-0.5 transition-all duration-300 ease-out;
  }

  /* Card Styles */
  .card {
    @apply card-gradient rounded-2xl shadow-card hover:shadow-green-soft transform hover:-translate-y-1 transition-all duration-300 ease-out;
  }

  .card-featured {
    @apply card border-2 border-emerald-200 shadow-green-medium hover:shadow-green-strong;
  }

  /* Input Styles */
  .input-primary {
    @apply w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-100 outline-none transition-all duration-300;
  }
}

@layer utilities {
  /* Animation Keyframes */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes float-delayed {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-15px);
    }
  }

  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes pulse-green {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(var(--color-primary), 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(var(--color-primary), 0);
    }
  }

  /* Animation Classes */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 4s ease-in-out infinite;
  }

  .animate-shine {
    position: relative;
    overflow: hidden;
  }

  .animate-shine::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shine 2s infinite;
  }

  .animate-pulse-green {
    animation: pulse-green 2s infinite;
  }

  /* Responsive Text Sizes */
  .text-responsive-xs {
    @apply text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-base md:text-lg;
  }

  .text-responsive-md {
    @apply text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  .text-responsive-3xl {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  /* Responsive Spacing */
  .space-responsive {
    @apply space-y-4 md:space-y-6 lg:space-y-8;
  }

  .p-responsive {
    @apply p-4 md:p-6 lg:p-8;
  }

  .px-responsive {
    @apply px-4 md:px-6 lg:px-8;
  }

  .py-responsive {
    @apply py-8 md:py-12 lg:py-16;
  }

  /* Container Utilities */
  .container-responsive {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Focus Styles for Accessibility */
  .focus-visible {
    @apply focus:outline-none focus:ring-4 focus:ring-emerald-100 focus:ring-offset-2;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-float-delayed,
  .animate-shine,
  .animate-pulse-green {
    animation: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}
