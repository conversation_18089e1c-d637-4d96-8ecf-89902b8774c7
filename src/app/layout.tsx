import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const poppins = Poppins({ 
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-poppins',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'CashBoost - Compare Cashback Rates & Maximize Your Savings',
  description: 'Find the best cashback rates across all major platforms. Compare real-time cashback percentages, discover exclusive offers, and maximize your savings with our comprehensive cashback comparison tool.',
  keywords: 'cashback comparison, cashback rates, online shopping, savings, deals, cashback platforms, money back, shopping rewards',
  authors: [{ name: 'CashBoost Team' }],
  creator: 'CashBoost',
  publisher: 'CashBoost',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://cashboost.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'CashBoost - Compare Cashback Rates & Maximize Your Savings',
    description: 'Find the best cashback rates across all major platforms. Compare real-time cashback percentages and maximize your savings.',
    url: 'https://cashboost.com',
    siteName: 'CashBoost',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'CashBoost - Cashback Comparison Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CashBoost - Compare Cashback Rates & Maximize Your Savings',
    description: 'Find the best cashback rates across all major platforms. Compare real-time cashback percentages and maximize your savings.',
    images: ['/og-image.jpg'],
    creator: '@cashboost',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#10B981" />
        <meta name="msapplication-TileColor" content="#10B981" />
      </head>
      <body className="font-inter antialiased bg-green-50 text-gray-900 overflow-x-hidden">
        <div className="min-h-screen">
          {children}
        </div>
      </body>
    </html>
  )
}
