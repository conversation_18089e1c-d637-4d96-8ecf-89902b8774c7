"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FloatingCashbackCards.tsx":
/*!**************************************************!*\
  !*** ./src/components/FloatingCashbackCards.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingCashbackCards; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst floatingCards = [\n    {\n        id: 1,\n        store: \"Amazon\",\n        logo: \"/logos/amazon.svg\",\n        rate: \"5.5%\",\n        platform: \"Rakuten\",\n        position: {\n            top: \"15%\",\n            left: \"10%\"\n        },\n        delay: 0,\n        color: \"#FF9900\"\n    },\n    {\n        id: 2,\n        store: \"Nike\",\n        logo: \"/logos/nike.svg\",\n        rate: \"8.0%\",\n        platform: \"Honey\",\n        position: {\n            top: \"25%\",\n            right: \"15%\"\n        },\n        delay: 0.5,\n        color: \"#000000\"\n    },\n    {\n        id: 3,\n        store: \"Target\",\n        logo: \"/logos/target.svg\",\n        rate: \"3.5%\",\n        platform: \"TopCashback\",\n        position: {\n            bottom: \"30%\",\n            left: \"8%\"\n        },\n        delay: 1,\n        color: \"#CC0000\"\n    },\n    {\n        id: 4,\n        store: \"Best Buy\",\n        logo: \"/logos/bestbuy.svg\",\n        rate: \"4.2%\",\n        platform: \"Cashback Monitor\",\n        position: {\n            bottom: \"20%\",\n            right: \"12%\"\n        },\n        delay: 1.5,\n        color: \"#0046BE\"\n    },\n    {\n        id: 5,\n        store: \"Walmart\",\n        logo: \"/logos/walmart.svg\",\n        rate: \"2.8%\",\n        platform: \"BeFrugal\",\n        position: {\n            top: \"45%\",\n            left: \"5%\"\n        },\n        delay: 2,\n        color: \"#004C91\"\n    },\n    {\n        id: 6,\n        store: \"Apple\",\n        logo: \"/logos/apple.svg\",\n        rate: \"1.5%\",\n        platform: \"Rakuten\",\n        position: {\n            top: \"35%\",\n            right: \"8%\"\n        },\n        delay: 2.5,\n        color: \"#000000\"\n    }\n];\nfunction FloatingCashbackCards() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n        children: [\n            floatingCards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.8,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: [\n                            0,\n                            1,\n                            1,\n                            0.7\n                        ],\n                        scale: [\n                            0.8,\n                            1,\n                            1,\n                            0.9\n                        ],\n                        y: [\n                            50,\n                            0,\n                            -10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 4,\n                        delay: card.delay,\n                        repeat: Infinity,\n                        repeatType: \"reverse\",\n                        ease: \"easeInOut\"\n                    },\n                    className: \"absolute hidden lg:block\",\n                    style: card.position,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-gradient p-4 rounded-2xl shadow-green-soft border border-white/20 backdrop-blur-sm min-w-[200px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-xl flex items-center justify-center shadow-sm\",\n                                        style: {\n                                            backgroundColor: \"\".concat(card.color, \"15\")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded\",\n                                            style: {\n                                                backgroundColor: card.color\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 text-sm\",\n                                                children: card.store\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"via \",\n                                                    card.platform\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-poppins font-bold text-emerald-600 mb-1\",\n                                        children: card.rate\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 font-medium\",\n                                        children: \"Cashback Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.1,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"flex items-center space-x-1 text-xs text-emerald-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Trending\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, card.id, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    1,\n                    2,\n                    3\n                ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.5\n                        },\n                        animate: {\n                            opacity: [\n                                0,\n                                0.6,\n                                0.6,\n                                0.3\n                            ],\n                            scale: [\n                                0.5,\n                                0.8,\n                                0.8,\n                                0.6\n                            ],\n                            y: [\n                                30,\n                                0,\n                                -5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 3,\n                            delay: index * 0.5,\n                            repeat: Infinity,\n                            repeatType: \"reverse\",\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute\",\n                        style: {\n                            top: \"\".concat(20 + index * 25, \"%\"),\n                            [index % 2 === 0 ? \"left\" : \"right\"]: \"5%\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glass-green p-3 rounded-xl shadow-green-soft\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold text-emerald-600\",\n                                    children: index === 1 ? \"5.5%\" : index === 2 ? \"8.0%\" : \"3.5%\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-emerald-700\",\n                                    children: \"Cashback\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    }, \"mobile-\".concat(index), false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    ...Array(20)\n                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: Math.random() * 1200,\n                            y: Math.random() * 800\n                        },\n                        animate: {\n                            opacity: [\n                                0,\n                                0.3,\n                                0\n                            ],\n                            x: Math.random() * 1200,\n                            y: Math.random() * 800\n                        },\n                        transition: {\n                            duration: Math.random() * 10 + 10,\n                            repeat: Infinity,\n                            repeatType: \"reverse\",\n                            ease: \"linear\",\n                            delay: Math.random() * 5\n                        },\n                        className: \"absolute w-1 h-1 bg-white rounded-full\"\n                    }, \"particle-\".concat(index), false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            [\n                1,\n                2,\n                3,\n                4,\n                5\n            ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 100,\n                        x: Math.random() * ( true ? window.innerWidth : 0)\n                    },\n                    animate: {\n                        opacity: [\n                            0,\n                            0.4,\n                            0\n                        ],\n                        y: -100\n                    },\n                    transition: {\n                        duration: 8,\n                        repeat: Infinity,\n                        delay: index * 2,\n                        ease: \"linear\"\n                    },\n                    className: \"absolute text-white/20 text-2xl font-bold pointer-events-none\",\n                    children: \"$\"\n                }, \"dollar-\".concat(index), false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c = FloatingCashbackCards;\nvar _c;\n$RefreshReg$(_c, \"FloatingCashbackCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FloatingCashbackCards.tsx\n"));

/***/ })

});