"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PlatformComparisonSection.tsx":
/*!******************************************************!*\
  !*** ./src/components/PlatformComparisonSection.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlatformComparisonSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst platforms = [\n    {\n        id: \"meliuz\",\n        name: \"Meliuz\",\n        logo: \"/logos/meliuz.svg\",\n        rating: 4.7,\n        totalStores: 2800,\n        averageCashback: 4.1,\n        minPayout: 20.00,\n        payoutTime: \"2-3 days\",\n        payoutMethods: [\n            \"PIX\",\n            \"Bank Transfer\",\n            \"Gift Cards\"\n        ],\n        signupBonus: 15,\n        referralBonus: 20,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Excellent\"\n        },\n        pros: [\n            \"Leading Brazilian platform\",\n            \"PIX payments\",\n            \"High cashback rates\"\n        ],\n        cons: [\n            \"Brazil-focused stores\",\n            \"Higher minimum payout\"\n        ],\n        color: \"from-purple-500 to-indigo-500\"\n    },\n    {\n        id: \"intershopping\",\n        name: \"Inter Shopping\",\n        logo: \"/logos/intershopping.svg\",\n        rating: 4.6,\n        totalStores: 1800,\n        averageCashback: 3.5,\n        minPayout: 10.00,\n        payoutTime: \"1-2 days\",\n        payoutMethods: [\n            \"PIX\",\n            \"Inter Account\",\n            \"Bank Transfer\"\n        ],\n        signupBonus: 10,\n        referralBonus: 15,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Good\"\n        },\n        pros: [\n            \"Instant PIX payments\",\n            \"Low minimum payout\",\n            \"Bank integration\"\n        ],\n        cons: [\n            \"Smaller store selection\",\n            \"Brazil-only focus\"\n        ],\n        color: \"from-emerald-500 to-green-500\"\n    },\n    {\n        id: \"rakuten\",\n        name: \"Rakuten\",\n        logo: \"/logos/rakuten.svg\",\n        rating: 4.8,\n        totalStores: 3500,\n        averageCashback: 3.2,\n        minPayout: 5.01,\n        payoutTime: \"3-5 days\",\n        payoutMethods: [\n            \"PayPal\",\n            \"Check\"\n        ],\n        signupBonus: 10,\n        referralBonus: 25,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Excellent\"\n        },\n        pros: [\n            \"Largest international network\",\n            \"Reliable payouts\",\n            \"Great browser extension\"\n        ],\n        cons: [\n            \"USD payments only\",\n            \"Limited Brazilian stores\"\n        ],\n        color: \"from-orange-500 to-yellow-500\"\n    },\n    {\n        id: \"bancopan\",\n        name: \"Banco Pan\",\n        logo: \"/logos/bancopan.svg\",\n        rating: 4.4,\n        totalStores: 1500,\n        averageCashback: 2.8,\n        minPayout: 25,\n        payoutTime: \"1-2 days\",\n        payoutMethods: [\n            \"PIX\",\n            \"Pan Account\",\n            \"Bank Transfer\"\n        ],\n        signupBonus: 20,\n        referralBonus: 15,\n        features: {\n            browserExtension: false,\n            mobileApp: true,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Good\"\n        },\n        pros: [\n            \"Bank integration\",\n            \"Fast PIX payments\",\n            \"Good signup bonus\"\n        ],\n        cons: [\n            \"No browser extension\",\n            \"Limited store selection\"\n        ],\n        color: \"from-blue-500 to-cyan-500\"\n    }\n];\nconst comparisonFeatures = [\n    {\n        name: \"Browser Extension\",\n        key: \"browserExtension\"\n    },\n    {\n        name: \"Mobile App\",\n        key: \"mobileApp\"\n    },\n    {\n        name: \"Price Comparison\",\n        key: \"priceComparison\"\n    },\n    {\n        name: \"Coupon Codes\",\n        key: \"couponCodes\"\n    },\n    {\n        name: \"In-Store Offers\",\n        key: \"inStoreOffers\"\n    }\n];\nfunction PlatformComparisonSection() {\n    const renderStars = (rating)=>{\n        const stars = [];\n        const fullStars = Math.floor(rating);\n        const hasHalfStar = rating % 1 !== 0;\n        for(let i = 0; i < fullStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-4 h-4 text-yellow-400\"\n            }, i, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this));\n        }\n        if (hasHalfStar) {\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-4 h-4 text-yellow-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"half\", true, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this));\n        }\n        const remainingStars = 5 - Math.ceil(rating);\n        for(let i = 0; i < remainingStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4 text-gray-300\"\n            }, \"empty-\".concat(i), false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this));\n        }\n        return stars;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"platforms\",\n        className: \"py-responsive bg-gradient-to-br from-gray-50 to-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-responsive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white text-gray-700 font-medium text-sm mb-6 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                \"Platform Comparison\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Brazilian & International\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Platform Comparison\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-sm text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Compare features, payout methods, and average rates across Brazilian and international cashback platforms. See which platform works best for your shopping habits.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16\",\n                    children: platforms.map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br \".concat(platform.color, \" shadow-lg mb-4\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: platform.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-poppins font-bold text-gray-900 mb-2\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-1 mb-1\",\n                                            children: renderStars(platform.rating)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 font-medium\",\n                                            children: [\n                                                platform.rating,\n                                                \" rating\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Stores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        platform.totalStores.toLocaleString(),\n                                                        \"+\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Avg. Cashback\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-emerald-600\",\n                                                    children: [\n                                                        platform.averageCashback,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Min. Payout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        \"$\",\n                                                        platform.minPayout\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Payout Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: platform.payoutTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: comparisonFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: feature.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        platform.features[feature.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 text-emerald-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, feature.key, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                (platform.signupBonus > 0 || platform.referralBonus > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 rounded-xl p-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-emerald-700 mb-2\",\n                                            children: \"Bonuses\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        platform.signupBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600 mb-1\",\n                                            children: [\n                                                \"$\",\n                                                platform.signupBonus,\n                                                \" signup bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, this),\n                                        platform.referralBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600\",\n                                            children: [\n                                                \"$\",\n                                                platform.referralBonus,\n                                                \" referral bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full btn-primary justify-center group-hover:shadow-green-strong transition-all duration-200\",\n                                    children: \"Compare Rates\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, platform.id, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center bg-white rounded-3xl p-8 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: \"Why Choose When You Can Compare?\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                            children: \"Instead of signing up for multiple platforms, use CashBoost to instantly see which one offers the best rate for each store.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4\",\n                            children: \"Start Comparing All Platforms\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c = PlatformComparisonSection;\nvar _c;\n$RefreshReg$(_c, \"PlatformComparisonSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PlatformComparisonSection.tsx\n"));

/***/ })

});