"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PlatformComparisonSection.tsx":
/*!******************************************************!*\
  !*** ./src/components/PlatformComparisonSection.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlatformComparisonSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst platforms = [\n    {\n        id: 'meliuz',\n        name: 'Meliuz',\n        logo: '/logos/meliuz.svg',\n        rating: 4.7,\n        totalStores: 2800,\n        averageCashback: 4.1,\n        minPayout: 20.00,\n        payoutTime: '2-3 days',\n        payoutMethods: [\n            'PIX',\n            'Bank Transfer',\n            'Gift Cards'\n        ],\n        signupBonus: 15,\n        referralBonus: 20,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: 'Excellent'\n        },\n        pros: [\n            'Leading Brazilian platform',\n            'PIX payments',\n            'High cashback rates'\n        ],\n        cons: [\n            'Brazil-focused stores',\n            'Higher minimum payout'\n        ],\n        color: 'from-purple-500 to-indigo-500'\n    },\n    {\n        id: 'intershopping',\n        name: 'Inter Shopping',\n        logo: '/logos/intershopping.svg',\n        rating: 4.6,\n        totalStores: 1800,\n        averageCashback: 3.5,\n        minPayout: 10.00,\n        payoutTime: '1-2 days',\n        payoutMethods: [\n            'PIX',\n            'Inter Account',\n            'Bank Transfer'\n        ],\n        signupBonus: 10,\n        referralBonus: 15,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: 'Good'\n        },\n        pros: [\n            'Instant PIX payments',\n            'Low minimum payout',\n            'Bank integration'\n        ],\n        cons: [\n            'Smaller store selection',\n            'Brazil-only focus'\n        ],\n        color: 'from-emerald-500 to-green-500'\n    },\n    {\n        id: 'rakuten',\n        name: 'Rakuten',\n        logo: '/logos/rakuten.svg',\n        rating: 4.8,\n        totalStores: 3500,\n        averageCashback: 3.2,\n        minPayout: 5.01,\n        payoutTime: '3-5 days',\n        payoutMethods: [\n            'PayPal',\n            'Check'\n        ],\n        signupBonus: 10,\n        referralBonus: 25,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: 'Excellent'\n        },\n        pros: [\n            'Largest international network',\n            'Reliable payouts',\n            'Great browser extension'\n        ],\n        cons: [\n            'USD payments only',\n            'Limited Brazilian stores'\n        ],\n        color: 'from-orange-500 to-yellow-500'\n    },\n    {\n        id: 'bancopan',\n        name: 'Banco Pan',\n        logo: '/logos/bancopan.svg',\n        rating: 4.4,\n        totalStores: 1500,\n        averageCashback: 2.8,\n        minPayout: 25,\n        payoutTime: '1-2 days',\n        payoutMethods: [\n            'PIX',\n            'Pan Account',\n            'Bank Transfer'\n        ],\n        signupBonus: 20,\n        referralBonus: 15,\n        features: {\n            browserExtension: false,\n            mobileApp: true,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: 'Good'\n        },\n        pros: [\n            'Bank integration',\n            'Fast PIX payments',\n            'Good signup bonus'\n        ],\n        cons: [\n            'No browser extension',\n            'Limited store selection'\n        ],\n        color: 'from-blue-500 to-cyan-500'\n    }\n];\nconst comparisonFeatures = [\n    {\n        name: 'Browser Extension',\n        key: 'browserExtension'\n    },\n    {\n        name: 'Mobile App',\n        key: 'mobileApp'\n    },\n    {\n        name: 'Price Comparison',\n        key: 'priceComparison'\n    },\n    {\n        name: 'Coupon Codes',\n        key: 'couponCodes'\n    },\n    {\n        name: 'In-Store Offers',\n        key: 'inStoreOffers'\n    }\n];\nfunction PlatformComparisonSection() {\n    const renderStars = (rating)=>{\n        const stars = [];\n        const fullStars = Math.floor(rating);\n        const hasHalfStar = rating % 1 !== 0;\n        for(let i = 0; i < fullStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-4 h-4 text-yellow-400\"\n            }, i, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this));\n        }\n        if (hasHalfStar) {\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-4 h-4 text-yellow-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"half\", true, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this));\n        }\n        const remainingStars = 5 - Math.ceil(rating);\n        for(let i = 0; i < remainingStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4 text-gray-300\"\n            }, \"empty-\".concat(i), false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this));\n        }\n        return stars;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"platforms\",\n        className: \"py-responsive bg-gradient-to-br from-gray-50 to-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-responsive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white text-gray-700 font-medium text-sm mb-6 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                \"Compara\\xe7\\xe3o de Plataformas\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Brazilian & International\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Platform Comparison\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-sm text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Compare features, payout methods, and average rates across Brazilian and international cashback platforms. See which platform works best for your shopping habits.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16\",\n                    children: platforms.map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br \".concat(platform.color, \" shadow-lg mb-4\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: platform.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-poppins font-bold text-gray-900 mb-2\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-1 mb-1\",\n                                            children: renderStars(platform.rating)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 font-medium\",\n                                            children: [\n                                                platform.rating,\n                                                \" rating\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Stores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        platform.totalStores.toLocaleString(),\n                                                        \"+\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Avg. Cashback\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-emerald-600\",\n                                                    children: [\n                                                        platform.averageCashback,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Min. Payout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        \"$\",\n                                                        platform.minPayout\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Payout Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: platform.payoutTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: comparisonFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: feature.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        platform.features[feature.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 text-emerald-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, feature.key, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                (platform.signupBonus > 0 || platform.referralBonus > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 rounded-xl p-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-emerald-700 mb-2\",\n                                            children: \"Bonuses\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        platform.signupBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600 mb-1\",\n                                            children: [\n                                                \"$\",\n                                                platform.signupBonus,\n                                                \" signup bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, this),\n                                        platform.referralBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600\",\n                                            children: [\n                                                \"$\",\n                                                platform.referralBonus,\n                                                \" referral bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full btn-primary justify-center group-hover:shadow-green-strong transition-all duration-200\",\n                                    children: \"Compare Rates\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, platform.id, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center bg-white rounded-3xl p-8 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: \"All Platforms, One Comparison\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                            children: \"See rates from Brazilian platforms like Meliuz and Inter Shopping alongside international options like Rakuten - all in one place, instantly.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4\",\n                            children: \"Compare All Rates Now\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c = PlatformComparisonSection;\nvar _c;\n$RefreshReg$(_c, \"PlatformComparisonSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PlatformComparisonSection.tsx\n"));

/***/ })

});