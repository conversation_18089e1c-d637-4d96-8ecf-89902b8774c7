"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PlatformComparisonSection.tsx":
/*!******************************************************!*\
  !*** ./src/components/PlatformComparisonSection.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlatformComparisonSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst platforms = [\n    {\n        id: \"meliuz\",\n        name: \"Meliuz\",\n        logo: \"/logos/meliuz.svg\",\n        rating: 4.7,\n        totalStores: 2800,\n        averageCashback: 4.1,\n        minPayout: 20.00,\n        payoutTime: \"2-3 days\",\n        payoutMethods: [\n            \"PIX\",\n            \"Bank Transfer\",\n            \"Gift Cards\"\n        ],\n        signupBonus: 15,\n        referralBonus: 20,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Excellent\"\n        },\n        pros: [\n            \"Leading Brazilian platform\",\n            \"PIX payments\",\n            \"High cashback rates\"\n        ],\n        cons: [\n            \"Brazil-focused stores\",\n            \"Higher minimum payout\"\n        ],\n        color: \"from-purple-500 to-indigo-500\"\n    },\n    {\n        id: \"topcashback\",\n        name: \"TopCashback\",\n        logo: \"/logos/topcashback.svg\",\n        rating: 4.7,\n        totalStores: 4000,\n        averageCashback: 3.8,\n        minPayout: 0.01,\n        payoutTime: \"2-3 days\",\n        payoutMethods: [\n            \"PayPal\",\n            \"Bank Transfer\",\n            \"Gift Cards\"\n        ],\n        signupBonus: 5,\n        referralBonus: 15,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: false,\n            customerSupport: \"Good\"\n        },\n        pros: [\n            \"Highest average rates\",\n            \"Low minimum payout\",\n            \"Fast payments\"\n        ],\n        cons: [\n            \"Fewer bonus features\",\n            \"Limited mobile app features\"\n        ],\n        color: \"from-emerald-500 to-green-500\"\n    },\n    {\n        id: \"honey\",\n        name: \"Honey\",\n        logo: \"/logos/honey.svg\",\n        rating: 4.5,\n        totalStores: 2800,\n        averageCashback: 2.1,\n        minPayout: 20,\n        payoutTime: \"5-7 days\",\n        payoutMethods: [\n            \"PayPal\",\n            \"Gift Cards\"\n        ],\n        signupBonus: 0,\n        referralBonus: 5,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: false,\n            customerSupport: \"Good\"\n        },\n        pros: [\n            \"Automatic coupon finding\",\n            \"Easy to use\",\n            \"Price tracking\"\n        ],\n        cons: [\n            \"Lower cashback rates\",\n            \"Higher minimum payout\"\n        ],\n        color: \"from-orange-500 to-yellow-500\"\n    },\n    {\n        id: \"befrugal\",\n        name: \"BeFrugal\",\n        logo: \"/logos/befrugal.svg\",\n        rating: 4.3,\n        totalStores: 2200,\n        averageCashback: 2.8,\n        minPayout: 25,\n        payoutTime: \"4-6 days\",\n        payoutMethods: [\n            \"PayPal\",\n            \"Check\",\n            \"Amazon Gift Card\"\n        ],\n        signupBonus: 10,\n        referralBonus: 10,\n        features: {\n            browserExtension: true,\n            mobileApp: false,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Average\"\n        },\n        pros: [\n            \"Good signup bonus\",\n            \"In-store offers\",\n            \"Competitive rates\"\n        ],\n        cons: [\n            \"No mobile app\",\n            \"Smaller store selection\"\n        ],\n        color: \"from-blue-500 to-cyan-500\"\n    }\n];\nconst comparisonFeatures = [\n    {\n        name: \"Browser Extension\",\n        key: \"browserExtension\"\n    },\n    {\n        name: \"Mobile App\",\n        key: \"mobileApp\"\n    },\n    {\n        name: \"Price Comparison\",\n        key: \"priceComparison\"\n    },\n    {\n        name: \"Coupon Codes\",\n        key: \"couponCodes\"\n    },\n    {\n        name: \"In-Store Offers\",\n        key: \"inStoreOffers\"\n    }\n];\nfunction PlatformComparisonSection() {\n    const renderStars = (rating)=>{\n        const stars = [];\n        const fullStars = Math.floor(rating);\n        const hasHalfStar = rating % 1 !== 0;\n        for(let i = 0; i < fullStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-4 h-4 text-yellow-400\"\n            }, i, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this));\n        }\n        if (hasHalfStar) {\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-4 h-4 text-yellow-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"half\", true, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this));\n        }\n        const remainingStars = 5 - Math.ceil(rating);\n        for(let i = 0; i < remainingStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4 text-gray-300\"\n            }, \"empty-\".concat(i), false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this));\n        }\n        return stars;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"platforms\",\n        className: \"py-responsive bg-gradient-to-br from-gray-50 to-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-responsive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white text-gray-700 font-medium text-sm mb-6 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                \"Platform Comparison\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Compare All\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Cashback Platforms\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-sm text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Not all cashback platforms are created equal. See how they stack up across key features, rates, and user experience.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16\",\n                    children: platforms.map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br \".concat(platform.color, \" shadow-lg mb-4\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: platform.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-poppins font-bold text-gray-900 mb-2\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-1 mb-1\",\n                                            children: renderStars(platform.rating)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 font-medium\",\n                                            children: [\n                                                platform.rating,\n                                                \" rating\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Stores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        platform.totalStores.toLocaleString(),\n                                                        \"+\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Avg. Cashback\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-emerald-600\",\n                                                    children: [\n                                                        platform.averageCashback,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Min. Payout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        \"$\",\n                                                        platform.minPayout\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Payout Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: platform.payoutTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: comparisonFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: feature.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        platform.features[feature.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 text-emerald-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, feature.key, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                (platform.signupBonus > 0 || platform.referralBonus > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 rounded-xl p-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-emerald-700 mb-2\",\n                                            children: \"Bonuses\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        platform.signupBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600 mb-1\",\n                                            children: [\n                                                \"$\",\n                                                platform.signupBonus,\n                                                \" signup bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, this),\n                                        platform.referralBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600\",\n                                            children: [\n                                                \"$\",\n                                                platform.referralBonus,\n                                                \" referral bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full btn-primary justify-center group-hover:shadow-green-strong transition-all duration-200\",\n                                    children: \"Compare Rates\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, platform.id, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center bg-white rounded-3xl p-8 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: \"Why Choose When You Can Compare?\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                            children: \"Instead of signing up for multiple platforms, use CashBoost to instantly see which one offers the best rate for each store.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4\",\n                            children: \"Start Comparing All Platforms\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c = PlatformComparisonSection;\nvar _c;\n$RefreshReg$(_c, \"PlatformComparisonSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1BsYXRmb3JtQ29tcGFyaXNvblNlY3Rpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVzQztBQVNGO0FBQ2lDO0FBRXJFLE1BQU1NLFlBQVk7SUFDaEI7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGlCQUFpQjtRQUNqQkMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLGVBQWU7WUFBQztZQUFPO1lBQWlCO1NBQWE7UUFDckRDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxVQUFVO1lBQ1JDLGtCQUFrQjtZQUNsQkMsV0FBVztZQUNYQyxpQkFBaUI7WUFDakJDLGFBQWE7WUFDYkMsZUFBZTtZQUNmQyxpQkFBaUI7UUFDbkI7UUFDQUMsTUFBTTtZQUFDO1lBQThCO1lBQWdCO1NBQXNCO1FBQzNFQyxNQUFNO1lBQUM7WUFBeUI7U0FBd0I7UUFDeERDLE9BQU87SUFDVDtJQUNBO1FBQ0VwQixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsaUJBQWlCO1FBQ2pCQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsZUFBZTtZQUFDO1lBQVU7WUFBaUI7U0FBYTtRQUN4REMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLFVBQVU7WUFDUkMsa0JBQWtCO1lBQ2xCQyxXQUFXO1lBQ1hDLGlCQUFpQjtZQUNqQkMsYUFBYTtZQUNiQyxlQUFlO1lBQ2ZDLGlCQUFpQjtRQUNuQjtRQUNBQyxNQUFNO1lBQUM7WUFBeUI7WUFBc0I7U0FBZ0I7UUFDdEVDLE1BQU07WUFBQztZQUF3QjtTQUE4QjtRQUM3REMsT0FBTztJQUNUO0lBQ0E7UUFDRXBCLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsYUFBYTtRQUNiQyxpQkFBaUI7UUFDakJDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxlQUFlO1lBQUM7WUFBVTtTQUFhO1FBQ3ZDQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsVUFBVTtZQUNSQyxrQkFBa0I7WUFDbEJDLFdBQVc7WUFDWEMsaUJBQWlCO1lBQ2pCQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZkMsaUJBQWlCO1FBQ25CO1FBQ0FDLE1BQU07WUFBQztZQUE0QjtZQUFlO1NBQWlCO1FBQ25FQyxNQUFNO1lBQUM7WUFBd0I7U0FBd0I7UUFDdkRDLE9BQU87SUFDVDtJQUNBO1FBQ0VwQixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsaUJBQWlCO1FBQ2pCQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsZUFBZTtZQUFDO1lBQVU7WUFBUztTQUFtQjtRQUN0REMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLFVBQVU7WUFDUkMsa0JBQWtCO1lBQ2xCQyxXQUFXO1lBQ1hDLGlCQUFpQjtZQUNqQkMsYUFBYTtZQUNiQyxlQUFlO1lBQ2ZDLGlCQUFpQjtRQUNuQjtRQUNBQyxNQUFNO1lBQUM7WUFBcUI7WUFBbUI7U0FBb0I7UUFDbkVDLE1BQU07WUFBQztZQUFpQjtTQUEwQjtRQUNsREMsT0FBTztJQUNUO0NBQ0Q7QUFFRCxNQUFNQyxxQkFBcUI7SUFDekI7UUFBRXBCLE1BQU07UUFBcUJxQixLQUFLO0lBQW1CO0lBQ3JEO1FBQUVyQixNQUFNO1FBQWNxQixLQUFLO0lBQVk7SUFDdkM7UUFBRXJCLE1BQU07UUFBb0JxQixLQUFLO0lBQWtCO0lBQ25EO1FBQUVyQixNQUFNO1FBQWdCcUIsS0FBSztJQUFjO0lBQzNDO1FBQUVyQixNQUFNO1FBQW1CcUIsS0FBSztJQUFnQjtDQUNqRDtBQUVjLFNBQVNDO0lBQ3RCLE1BQU1DLGNBQWMsQ0FBQ3JCO1FBQ25CLE1BQU1zQixRQUFRLEVBQUU7UUFDaEIsTUFBTUMsWUFBWUMsS0FBS0MsS0FBSyxDQUFDekI7UUFDN0IsTUFBTTBCLGNBQWMxQixTQUFTLE1BQU07UUFFbkMsSUFBSyxJQUFJMkIsSUFBSSxHQUFHQSxJQUFJSixXQUFXSSxJQUFLO1lBQ2xDTCxNQUFNTSxJQUFJLGVBQ1IsOERBQUNqQyxnR0FBYUE7Z0JBQVNrQyxXQUFVO2VBQWJGOzs7OztRQUV4QjtRQUVBLElBQUlELGFBQWE7WUFDZkosTUFBTU0sSUFBSSxlQUNSLDhEQUFDRTtnQkFBZUQsV0FBVTs7a0NBQ3hCLDhEQUFDckMsbUlBQVFBO3dCQUFDcUMsV0FBVTs7Ozs7O2tDQUNwQiw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNsQyxnR0FBYUE7NEJBQUNrQyxXQUFVOzs7Ozs7Ozs7Ozs7ZUFIcEI7Ozs7O1FBT2I7UUFFQSxNQUFNRSxpQkFBaUIsSUFBSVAsS0FBS1EsSUFBSSxDQUFDaEM7UUFDckMsSUFBSyxJQUFJMkIsSUFBSSxHQUFHQSxJQUFJSSxnQkFBZ0JKLElBQUs7WUFDdkNMLE1BQU1NLElBQUksZUFDUiw4REFBQ3BDLG1JQUFRQTtnQkFBb0JxQyxXQUFVO2VBQXhCLFNBQVcsT0FBRkY7Ozs7O1FBRTVCO1FBRUEsT0FBT0w7SUFDVDtJQUVBLHFCQUNFLDhEQUFDVztRQUFRcEMsSUFBRztRQUFZZ0MsV0FBVTtrQkFDaEMsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUViLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUN2QyxpREFBTUEsQ0FBQ3dDLEdBQUc7NEJBQ1RJLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTs0QkFBSTs0QkFDNUJDLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7NEJBQ3ZCWixXQUFVOzs4Q0FFViw4REFBQ3RDLG1JQUFZQTtvQ0FBQ3NDLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7c0NBSTNDLDhEQUFDdkMsaURBQU1BLENBQUNvRCxFQUFFOzRCQUNSUixTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QkMsYUFBYTtnQ0FBRUYsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDaENFLFlBQVk7Z0NBQUVDLFVBQVU7Z0NBQUtJLE9BQU87NEJBQUk7NEJBQ3hDSCxVQUFVO2dDQUFFQyxNQUFNOzRCQUFLOzRCQUN2QlosV0FBVTs7Z0NBQ1g7Z0NBQ2E7OENBQ1osOERBQUNlO29DQUFLZixXQUFVOzhDQUFnQjs7Ozs7Ozs7Ozs7O3NDQUdsQyw4REFBQ3ZDLGlEQUFNQSxDQUFDdUQsQ0FBQzs0QkFDUFgsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLGFBQWE7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQ2hDRSxZQUFZO2dDQUFFQyxVQUFVO2dDQUFLSSxPQUFPOzRCQUFJOzRCQUN4Q0gsVUFBVTtnQ0FBRUMsTUFBTTs0QkFBSzs0QkFDdkJaLFdBQVU7c0NBQ1g7Ozs7Ozs7Ozs7Ozs4QkFPSCw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ1pqQyxVQUFVa0QsR0FBRyxDQUFDLENBQUNDLFVBQVVDLHNCQUN4Qiw4REFBQzFELGlEQUFNQSxDQUFDd0MsR0FBRzs0QkFFVEksU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLGFBQWE7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQ2hDRSxZQUFZO2dDQUFFQyxVQUFVO2dDQUFLSSxPQUFPSyxRQUFROzRCQUFJOzRCQUNoRFIsVUFBVTtnQ0FBRUMsTUFBTTs0QkFBSzs0QkFDdkJaLFdBQVU7OzhDQUdWLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFXLG1GQUFrRyxPQUFma0IsU0FBUzlCLEtBQUssRUFBQztzREFDaEgsNEVBQUMyQjtnREFBS2YsV0FBVTswREFDYmtCLFNBQVNqRCxJQUFJLENBQUNtRCxNQUFNLENBQUM7Ozs7Ozs7Ozs7O3NEQUcxQiw4REFBQ0M7NENBQUdyQixXQUFVO3NEQUNYa0IsU0FBU2pELElBQUk7Ozs7OztzREFFaEIsOERBQUNnQzs0Q0FBSUQsV0FBVTtzREFDWlIsWUFBWTBCLFNBQVMvQyxNQUFNOzs7Ozs7c0RBRTlCLDhEQUFDNEM7NENBQUtmLFdBQVU7O2dEQUNia0IsU0FBUy9DLE1BQU07Z0RBQUM7Ozs7Ozs7Ozs7Ozs7OENBS3JCLDhEQUFDOEI7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNlO29EQUFLZixXQUFVOzhEQUF3Qjs7Ozs7OzhEQUN4Qyw4REFBQ2U7b0RBQUtmLFdBQVU7O3dEQUNia0IsU0FBUzlDLFdBQVcsQ0FBQ2tELGNBQWM7d0RBQUc7Ozs7Ozs7Ozs7Ozs7c0RBRzNDLDhEQUFDckI7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDZTtvREFBS2YsV0FBVTs4REFBd0I7Ozs7Ozs4REFDeEMsOERBQUNlO29EQUFLZixXQUFVOzt3REFDYmtCLFNBQVM3QyxlQUFlO3dEQUFDOzs7Ozs7Ozs7Ozs7O3NEQUc5Qiw4REFBQzRCOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ2U7b0RBQUtmLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3hDLDhEQUFDZTtvREFBS2YsV0FBVTs7d0RBQThCO3dEQUMxQ2tCLFNBQVM1QyxTQUFTOzs7Ozs7Ozs7Ozs7O3NEQUd4Qiw4REFBQzJCOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ2U7b0RBQUtmLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3hDLDhEQUFDZTtvREFBS2YsV0FBVTs4REFDYmtCLFNBQVMzQyxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTTFCLDhEQUFDMEI7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDdUI7NENBQUd2QixXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ1pYLG1CQUFtQjRCLEdBQUcsQ0FBQyxDQUFDTyx3QkFDdkIsOERBQUN2QjtvREFBc0JELFdBQVU7O3NFQUMvQiw4REFBQ2U7NERBQUtmLFdBQVU7c0VBQXlCd0IsUUFBUXZELElBQUk7Ozs7Ozt3REFDcERpRCxTQUFTdkMsUUFBUSxDQUFDNkMsUUFBUWxDLEdBQUcsQ0FBbUMsaUJBQy9ELDhEQUFDMUIsbUlBQVNBOzREQUFDb0MsV0FBVTs7Ozs7aUZBRXJCLDhEQUFDbkMsbUlBQVNBOzREQUFDbUMsV0FBVTs7Ozs7OzttREFMZndCLFFBQVFsQyxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7O2dDQWF6QjRCLENBQUFBLFNBQVN6QyxXQUFXLEdBQUcsS0FBS3lDLFNBQVN4QyxhQUFhLEdBQUcsb0JBQ3JELDhEQUFDdUI7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDdUI7NENBQUd2QixXQUFVO3NEQUE4Qzs7Ozs7O3dDQUMzRGtCLFNBQVN6QyxXQUFXLEdBQUcsbUJBQ3RCLDhEQUFDd0I7NENBQUlELFdBQVU7O2dEQUFnQztnREFDM0NrQixTQUFTekMsV0FBVztnREFBQzs7Ozs7Ozt3Q0FHMUJ5QyxTQUFTeEMsYUFBYSxHQUFHLG1CQUN4Qiw4REFBQ3VCOzRDQUFJRCxXQUFVOztnREFBMkI7Z0RBQ3RDa0IsU0FBU3hDLGFBQWE7Z0RBQUM7Ozs7Ozs7Ozs7Ozs7OENBT2pDLDhEQUFDK0M7b0NBQU96QixXQUFVOzhDQUFnRzs7Ozs7OzsyQkF4RjdHa0IsU0FBU2xELEVBQUU7Ozs7Ozs7Ozs7OEJBZ0d0Qiw4REFBQ1AsaURBQU1BLENBQUN3QyxHQUFHO29CQUNUSSxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHQyxHQUFHO29CQUFHO29CQUM3QkMsYUFBYTt3QkFBRUYsU0FBUzt3QkFBR0MsR0FBRztvQkFBRTtvQkFDaENFLFlBQVk7d0JBQUVDLFVBQVU7d0JBQUtJLE9BQU87b0JBQUk7b0JBQ3hDSCxVQUFVO3dCQUFFQyxNQUFNO29CQUFLO29CQUN2QlosV0FBVTs7c0NBRVYsOERBQUNxQjs0QkFBR3JCLFdBQVU7c0NBQXFEOzs7Ozs7c0NBR25FLDhEQUFDZ0I7NEJBQUVoQixXQUFVO3NDQUF1Qzs7Ozs7O3NDQUlwRCw4REFBQ3lCOzRCQUFPekIsV0FBVTtzQ0FBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzVEO0tBak13QlQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvUGxhdGZvcm1Db21wYXJpc29uU2VjdGlvbi50c3g/M2M5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IFxuICBDaGFydEJhckljb24sXG4gIFN0YXJJY29uLFxuICBDaGVja0ljb24sXG4gIFhNYXJrSWNvbixcbiAgQ3VycmVuY3lEb2xsYXJJY29uLFxuICBDbG9ja0ljb24sXG4gIFNoaWVsZENoZWNrSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnXG5pbXBvcnQgeyBTdGFySWNvbiBhcyBTdGFySWNvblNvbGlkIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9zb2xpZCdcblxuY29uc3QgcGxhdGZvcm1zID0gW1xuICB7XG4gICAgaWQ6ICdtZWxpdXonLFxuICAgIG5hbWU6ICdNZWxpdXonLFxuICAgIGxvZ286ICcvbG9nb3MvbWVsaXV6LnN2ZycsXG4gICAgcmF0aW5nOiA0LjcsXG4gICAgdG90YWxTdG9yZXM6IDI4MDAsXG4gICAgYXZlcmFnZUNhc2hiYWNrOiA0LjEsXG4gICAgbWluUGF5b3V0OiAyMC4wMCxcbiAgICBwYXlvdXRUaW1lOiAnMi0zIGRheXMnLFxuICAgIHBheW91dE1ldGhvZHM6IFsnUElYJywgJ0JhbmsgVHJhbnNmZXInLCAnR2lmdCBDYXJkcyddLFxuICAgIHNpZ251cEJvbnVzOiAxNSxcbiAgICByZWZlcnJhbEJvbnVzOiAyMCxcbiAgICBmZWF0dXJlczoge1xuICAgICAgYnJvd3NlckV4dGVuc2lvbjogdHJ1ZSxcbiAgICAgIG1vYmlsZUFwcDogdHJ1ZSxcbiAgICAgIHByaWNlQ29tcGFyaXNvbjogdHJ1ZSxcbiAgICAgIGNvdXBvbkNvZGVzOiB0cnVlLFxuICAgICAgaW5TdG9yZU9mZmVyczogdHJ1ZSxcbiAgICAgIGN1c3RvbWVyU3VwcG9ydDogJ0V4Y2VsbGVudCcsXG4gICAgfSxcbiAgICBwcm9zOiBbJ0xlYWRpbmcgQnJhemlsaWFuIHBsYXRmb3JtJywgJ1BJWCBwYXltZW50cycsICdIaWdoIGNhc2hiYWNrIHJhdGVzJ10sXG4gICAgY29uczogWydCcmF6aWwtZm9jdXNlZCBzdG9yZXMnLCAnSGlnaGVyIG1pbmltdW0gcGF5b3V0J10sXG4gICAgY29sb3I6ICdmcm9tLXB1cnBsZS01MDAgdG8taW5kaWdvLTUwMCcsXG4gIH0sXG4gIHtcbiAgICBpZDogJ3RvcGNhc2hiYWNrJyxcbiAgICBuYW1lOiAnVG9wQ2FzaGJhY2snLFxuICAgIGxvZ286ICcvbG9nb3MvdG9wY2FzaGJhY2suc3ZnJyxcbiAgICByYXRpbmc6IDQuNyxcbiAgICB0b3RhbFN0b3JlczogNDAwMCxcbiAgICBhdmVyYWdlQ2FzaGJhY2s6IDMuOCxcbiAgICBtaW5QYXlvdXQ6IDAuMDEsXG4gICAgcGF5b3V0VGltZTogJzItMyBkYXlzJyxcbiAgICBwYXlvdXRNZXRob2RzOiBbJ1BheVBhbCcsICdCYW5rIFRyYW5zZmVyJywgJ0dpZnQgQ2FyZHMnXSxcbiAgICBzaWdudXBCb251czogNSxcbiAgICByZWZlcnJhbEJvbnVzOiAxNSxcbiAgICBmZWF0dXJlczoge1xuICAgICAgYnJvd3NlckV4dGVuc2lvbjogdHJ1ZSxcbiAgICAgIG1vYmlsZUFwcDogdHJ1ZSxcbiAgICAgIHByaWNlQ29tcGFyaXNvbjogZmFsc2UsXG4gICAgICBjb3Vwb25Db2RlczogdHJ1ZSxcbiAgICAgIGluU3RvcmVPZmZlcnM6IGZhbHNlLFxuICAgICAgY3VzdG9tZXJTdXBwb3J0OiAnR29vZCcsXG4gICAgfSxcbiAgICBwcm9zOiBbJ0hpZ2hlc3QgYXZlcmFnZSByYXRlcycsICdMb3cgbWluaW11bSBwYXlvdXQnLCAnRmFzdCBwYXltZW50cyddLFxuICAgIGNvbnM6IFsnRmV3ZXIgYm9udXMgZmVhdHVyZXMnLCAnTGltaXRlZCBtb2JpbGUgYXBwIGZlYXR1cmVzJ10sXG4gICAgY29sb3I6ICdmcm9tLWVtZXJhbGQtNTAwIHRvLWdyZWVuLTUwMCcsXG4gIH0sXG4gIHtcbiAgICBpZDogJ2hvbmV5JyxcbiAgICBuYW1lOiAnSG9uZXknLFxuICAgIGxvZ286ICcvbG9nb3MvaG9uZXkuc3ZnJyxcbiAgICByYXRpbmc6IDQuNSxcbiAgICB0b3RhbFN0b3JlczogMjgwMCxcbiAgICBhdmVyYWdlQ2FzaGJhY2s6IDIuMSxcbiAgICBtaW5QYXlvdXQ6IDIwLFxuICAgIHBheW91dFRpbWU6ICc1LTcgZGF5cycsXG4gICAgcGF5b3V0TWV0aG9kczogWydQYXlQYWwnLCAnR2lmdCBDYXJkcyddLFxuICAgIHNpZ251cEJvbnVzOiAwLFxuICAgIHJlZmVycmFsQm9udXM6IDUsXG4gICAgZmVhdHVyZXM6IHtcbiAgICAgIGJyb3dzZXJFeHRlbnNpb246IHRydWUsXG4gICAgICBtb2JpbGVBcHA6IHRydWUsXG4gICAgICBwcmljZUNvbXBhcmlzb246IHRydWUsXG4gICAgICBjb3Vwb25Db2RlczogdHJ1ZSxcbiAgICAgIGluU3RvcmVPZmZlcnM6IGZhbHNlLFxuICAgICAgY3VzdG9tZXJTdXBwb3J0OiAnR29vZCcsXG4gICAgfSxcbiAgICBwcm9zOiBbJ0F1dG9tYXRpYyBjb3Vwb24gZmluZGluZycsICdFYXN5IHRvIHVzZScsICdQcmljZSB0cmFja2luZyddLFxuICAgIGNvbnM6IFsnTG93ZXIgY2FzaGJhY2sgcmF0ZXMnLCAnSGlnaGVyIG1pbmltdW0gcGF5b3V0J10sXG4gICAgY29sb3I6ICdmcm9tLW9yYW5nZS01MDAgdG8teWVsbG93LTUwMCcsXG4gIH0sXG4gIHtcbiAgICBpZDogJ2JlZnJ1Z2FsJyxcbiAgICBuYW1lOiAnQmVGcnVnYWwnLFxuICAgIGxvZ286ICcvbG9nb3MvYmVmcnVnYWwuc3ZnJyxcbiAgICByYXRpbmc6IDQuMyxcbiAgICB0b3RhbFN0b3JlczogMjIwMCxcbiAgICBhdmVyYWdlQ2FzaGJhY2s6IDIuOCxcbiAgICBtaW5QYXlvdXQ6IDI1LFxuICAgIHBheW91dFRpbWU6ICc0LTYgZGF5cycsXG4gICAgcGF5b3V0TWV0aG9kczogWydQYXlQYWwnLCAnQ2hlY2snLCAnQW1hem9uIEdpZnQgQ2FyZCddLFxuICAgIHNpZ251cEJvbnVzOiAxMCxcbiAgICByZWZlcnJhbEJvbnVzOiAxMCxcbiAgICBmZWF0dXJlczoge1xuICAgICAgYnJvd3NlckV4dGVuc2lvbjogdHJ1ZSxcbiAgICAgIG1vYmlsZUFwcDogZmFsc2UsXG4gICAgICBwcmljZUNvbXBhcmlzb246IGZhbHNlLFxuICAgICAgY291cG9uQ29kZXM6IHRydWUsXG4gICAgICBpblN0b3JlT2ZmZXJzOiB0cnVlLFxuICAgICAgY3VzdG9tZXJTdXBwb3J0OiAnQXZlcmFnZScsXG4gICAgfSxcbiAgICBwcm9zOiBbJ0dvb2Qgc2lnbnVwIGJvbnVzJywgJ0luLXN0b3JlIG9mZmVycycsICdDb21wZXRpdGl2ZSByYXRlcyddLFxuICAgIGNvbnM6IFsnTm8gbW9iaWxlIGFwcCcsICdTbWFsbGVyIHN0b3JlIHNlbGVjdGlvbiddLFxuICAgIGNvbG9yOiAnZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCcsXG4gIH0sXG5dXG5cbmNvbnN0IGNvbXBhcmlzb25GZWF0dXJlcyA9IFtcbiAgeyBuYW1lOiAnQnJvd3NlciBFeHRlbnNpb24nLCBrZXk6ICdicm93c2VyRXh0ZW5zaW9uJyB9LFxuICB7IG5hbWU6ICdNb2JpbGUgQXBwJywga2V5OiAnbW9iaWxlQXBwJyB9LFxuICB7IG5hbWU6ICdQcmljZSBDb21wYXJpc29uJywga2V5OiAncHJpY2VDb21wYXJpc29uJyB9LFxuICB7IG5hbWU6ICdDb3Vwb24gQ29kZXMnLCBrZXk6ICdjb3Vwb25Db2RlcycgfSxcbiAgeyBuYW1lOiAnSW4tU3RvcmUgT2ZmZXJzJywga2V5OiAnaW5TdG9yZU9mZmVycycgfSxcbl1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGxhdGZvcm1Db21wYXJpc29uU2VjdGlvbigpIHtcbiAgY29uc3QgcmVuZGVyU3RhcnMgPSAocmF0aW5nOiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBzdGFycyA9IFtdXG4gICAgY29uc3QgZnVsbFN0YXJzID0gTWF0aC5mbG9vcihyYXRpbmcpXG4gICAgY29uc3QgaGFzSGFsZlN0YXIgPSByYXRpbmcgJSAxICE9PSAwXG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGZ1bGxTdGFyczsgaSsrKSB7XG4gICAgICBzdGFycy5wdXNoKFxuICAgICAgICA8U3Rhckljb25Tb2xpZCBrZXk9e2l9IGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC15ZWxsb3ctNDAwXCIgLz5cbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAoaGFzSGFsZlN0YXIpIHtcbiAgICAgIHN0YXJzLnB1c2goXG4gICAgICAgIDxkaXYga2V5PVwiaGFsZlwiIGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgPFN0YXJJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG92ZXJmbG93LWhpZGRlbiB3LTEvMlwiPlxuICAgICAgICAgICAgPFN0YXJJY29uU29saWQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy00MDBcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zdCByZW1haW5pbmdTdGFycyA9IDUgLSBNYXRoLmNlaWwocmF0aW5nKVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVtYWluaW5nU3RhcnM7IGkrKykge1xuICAgICAgc3RhcnMucHVzaChcbiAgICAgICAgPFN0YXJJY29uIGtleT17YGVtcHR5LSR7aX1gfSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiBzdGFyc1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBpZD1cInBsYXRmb3Jtc1wiIGNsYXNzTmFtZT1cInB5LXJlc3BvbnNpdmUgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTUwIHRvLWdyYXktMTAwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1yZXNwb25zaXZlXCI+XG4gICAgICAgIHsvKiBTZWN0aW9uIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xNlwiPlxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgcm91bmRlZC1mdWxsIGJnLXdoaXRlIHRleHQtZ3JheS03MDAgZm9udC1tZWRpdW0gdGV4dC1zbSBtYi02IHNoYWRvdy1zbVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPENoYXJ0QmFySWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgUGxhdGZvcm0gQ29tcGFyaXNvblxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIDxtb3Rpb24uaDJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMSB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZXNwb25zaXZlLTJ4bCBmb250LXBvcHBpbnMgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ29tcGFyZSBBbGx7JyAnfVxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZ3JhZGllbnQtdGV4dFwiPkNhc2hiYWNrIFBsYXRmb3Jtczwvc3Bhbj5cbiAgICAgICAgICA8L21vdGlvbi5oMj5cblxuICAgICAgICAgIDxtb3Rpb24ucFxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlc3BvbnNpdmUtc20gdGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0b1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgTm90IGFsbCBjYXNoYmFjayBwbGF0Zm9ybXMgYXJlIGNyZWF0ZWQgZXF1YWwuIFNlZSBob3cgdGhleSBzdGFjayB1cCBhY3Jvc3MgXG4gICAgICAgICAgICBrZXkgZmVhdHVyZXMsIHJhdGVzLCBhbmQgdXNlciBleHBlcmllbmNlLlxuICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQbGF0Zm9ybSBDYXJkcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIHhsOmdyaWQtY29scy00IGdhcC04IG1iLTE2XCI+XG4gICAgICAgICAge3BsYXRmb3Jtcy5tYXAoKHBsYXRmb3JtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PXtwbGF0Zm9ybS5pZH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxuICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0zeGwgcC02IHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGdyb3VwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIFBsYXRmb3JtIEhlYWRlciAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy0xNiBoLTE2IHJvdW5kZWQtMnhsIGJnLWdyYWRpZW50LXRvLWJyICR7cGxhdGZvcm0uY29sb3J9IHNoYWRvdy1sZyBtYi00YH0+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXhsXCI+XG4gICAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybS5uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXBvcHBpbnMgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAge3BsYXRmb3JtLm5hbWV9XG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICB7cmVuZGVyU3RhcnMocGxhdGZvcm0ucmF0aW5nKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybS5yYXRpbmd9IHJhdGluZ1xuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEtleSBTdGF0cyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTQgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5TdG9yZXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3BsYXRmb3JtLnRvdGFsU3RvcmVzLnRvTG9jYWxlU3RyaW5nKCl9K1xuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5BdmcuIENhc2hiYWNrPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWVtZXJhbGQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybS5hdmVyYWdlQ2FzaGJhY2t9JVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5NaW4uIFBheW91dDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAke3BsYXRmb3JtLm1pblBheW91dH1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+UGF5b3V0IFRpbWU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3BsYXRmb3JtLnBheW91dFRpbWV9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBGZWF0dXJlcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwIG1iLTNcIj5GZWF0dXJlczwvaDQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIHtjb21wYXJpc29uRmVhdHVyZXMubWFwKChmZWF0dXJlKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtmZWF0dXJlLmtleX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e2ZlYXR1cmUubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAge3BsYXRmb3JtLmZlYXR1cmVzW2ZlYXR1cmUua2V5IGFzIGtleW9mIHR5cGVvZiBwbGF0Zm9ybS5mZWF0dXJlc10gPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1lbWVyYWxkLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxYTWFya0ljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQm9udXNlcyAqL31cbiAgICAgICAgICAgICAgeyhwbGF0Zm9ybS5zaWdudXBCb251cyA+IDAgfHwgcGxhdGZvcm0ucmVmZXJyYWxCb251cyA+IDApICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWVtZXJhbGQtNTAgcm91bmRlZC14bCBwLTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWVtZXJhbGQtNzAwIG1iLTJcIj5Cb251c2VzPC9oND5cbiAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybS5zaWdudXBCb251cyA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1lbWVyYWxkLTYwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgJHtwbGF0Zm9ybS5zaWdudXBCb251c30gc2lnbnVwIGJvbnVzXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybS5yZWZlcnJhbEJvbnVzID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWVtZXJhbGQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgJHtwbGF0Zm9ybS5yZWZlcnJhbEJvbnVzfSByZWZlcnJhbCBib251c1xuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIENUQSAqL31cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLXByaW1hcnkganVzdGlmeS1jZW50ZXIgZ3JvdXAtaG92ZXI6c2hhZG93LWdyZWVuLXN0cm9uZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICBDb21wYXJlIFJhdGVzXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQm90dG9tIENUQSAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuNCB9fVxuICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBiZy13aGl0ZSByb3VuZGVkLTN4bCBwLTggc2hhZG93LWxnXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXBvcHBpbnMgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgV2h5IENob29zZSBXaGVuIFlvdSBDYW4gQ29tcGFyZT9cbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNiBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgSW5zdGVhZCBvZiBzaWduaW5nIHVwIGZvciBtdWx0aXBsZSBwbGF0Zm9ybXMsIHVzZSBDYXNoQm9vc3QgdG8gaW5zdGFudGx5IFxuICAgICAgICAgICAgc2VlIHdoaWNoIG9uZSBvZmZlcnMgdGhlIGJlc3QgcmF0ZSBmb3IgZWFjaCBzdG9yZS5cbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSB0ZXh0LWxnIHB4LTggcHktNFwiPlxuICAgICAgICAgICAgU3RhcnQgQ29tcGFyaW5nIEFsbCBQbGF0Zm9ybXNcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApXG59XG4iXSwibmFtZXMiOlsibW90aW9uIiwiQ2hhcnRCYXJJY29uIiwiU3Rhckljb24iLCJDaGVja0ljb24iLCJYTWFya0ljb24iLCJTdGFySWNvblNvbGlkIiwicGxhdGZvcm1zIiwiaWQiLCJuYW1lIiwibG9nbyIsInJhdGluZyIsInRvdGFsU3RvcmVzIiwiYXZlcmFnZUNhc2hiYWNrIiwibWluUGF5b3V0IiwicGF5b3V0VGltZSIsInBheW91dE1ldGhvZHMiLCJzaWdudXBCb251cyIsInJlZmVycmFsQm9udXMiLCJmZWF0dXJlcyIsImJyb3dzZXJFeHRlbnNpb24iLCJtb2JpbGVBcHAiLCJwcmljZUNvbXBhcmlzb24iLCJjb3Vwb25Db2RlcyIsImluU3RvcmVPZmZlcnMiLCJjdXN0b21lclN1cHBvcnQiLCJwcm9zIiwiY29ucyIsImNvbG9yIiwiY29tcGFyaXNvbkZlYXR1cmVzIiwia2V5IiwiUGxhdGZvcm1Db21wYXJpc29uU2VjdGlvbiIsInJlbmRlclN0YXJzIiwic3RhcnMiLCJmdWxsU3RhcnMiLCJNYXRoIiwiZmxvb3IiLCJoYXNIYWxmU3RhciIsImkiLCJwdXNoIiwiY2xhc3NOYW1lIiwiZGl2IiwicmVtYWluaW5nU3RhcnMiLCJjZWlsIiwic2VjdGlvbiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsIndoaWxlSW5WaWV3IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwidmlld3BvcnQiLCJvbmNlIiwiaDIiLCJkZWxheSIsInNwYW4iLCJwIiwibWFwIiwicGxhdGZvcm0iLCJpbmRleCIsImNoYXJBdCIsImgzIiwidG9Mb2NhbGVTdHJpbmciLCJoNCIsImZlYXR1cmUiLCJidXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PlatformComparisonSection.tsx\n"));

/***/ })

});