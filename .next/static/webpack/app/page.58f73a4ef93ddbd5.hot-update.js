"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HowItWorksSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/HowItWorksSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HowItWorksSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CurrencyDollarIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CurrencyDollarIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CurrencyDollarIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CurrencyDollarIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst steps = [\n    {\n        id: 1,\n        title: 'Busque e Compare',\n        description: 'Digite o nome de qualquer loja ou navegue por categoria para ver taxas de cashback em tempo real de todas as principais plataformas.',\n        icon: _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        color: 'from-blue-500 to-cyan-500',\n        features: [\n            '500+ lojas',\n            'Taxas em tempo real',\n            'Todas as plataformas'\n        ]\n    },\n    {\n        id: 2,\n        title: 'Encontre as Melhores Taxas',\n        description: 'Nosso mecanismo de comparação inteligente mostra instantaneamente qual plataforma oferece o maior cashback para sua compra.',\n        icon: _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: 'from-emerald-500 to-green-500',\n        features: [\n            'Comparação inteligente',\n            'Análise de tendências',\n            'Alertas de taxa'\n        ]\n    },\n    {\n        id: 3,\n        title: 'Comece a Ganhar',\n        description: 'Clique na plataforma escolhida e comece a ganhar o máximo de cashback em cada compra que fizer.',\n        icon: _barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: 'from-yellow-500 to-orange-500',\n        features: [\n            'Ganhos máximos',\n            'Rastreamento instantâneo',\n            'Pagamentos fáceis'\n        ]\n    }\n];\nconst benefits = [\n    'Save time comparing rates manually',\n    'Never miss the best cashback deals',\n    'Maximize earnings on every purchase',\n    'Stay updated with rate changes',\n    'Access exclusive platform bonuses',\n    'Track your total savings'\n];\nfunction HowItWorksSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"how-it-works\",\n        className: \"py-responsive bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-responsive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm text-emerald-700 font-medium text-sm mb-6 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CurrencyDollarIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                \"Simple 3-Step Process\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"How\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"CashBoost\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                ' ',\n                                \"Works\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-sm text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Stop wasting time checking multiple cashback sites. Our platform does the heavy lifting, so you can focus on what matters most - saving money.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 mb-16\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative\",\n                            children: [\n                                index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block absolute top-16 left-full w-12 h-0.5 bg-gradient-to-r from-emerald-200 to-emerald-300 z-0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.05,\n                                                rotate: 5\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br \".concat(step.color, \" shadow-lg mb-6 group cursor-pointer\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                className: \"w-10 h-10 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-emerald-600\",\n                                                children: step.id\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-poppins font-bold text-gray-900 mb-4\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6 leading-relaxed\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: step.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.4,\n                                                        delay: index * 0.2 + featureIndex * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"inline-flex items-center px-3 py-1 bg-white/60 backdrop-blur-sm rounded-full text-sm font-medium text-emerald-700 mr-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        feature\n                                                    ]\n                                                }, feature, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, step.id, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-poppins font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose CashBoost?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Join thousands of smart shoppers who are already maximizing their cashback earnings\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.4,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"flex items-center space-x-3 p-4 rounded-xl hover:bg-emerald-50/50 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 text-white\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-700 font-medium\",\n                                            children: benefit\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, benefit, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mt-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4 animate-shine\",\n                            children: \"Start Comparing Rates Now\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mt-4\",\n                            children: \"Free to use • No signup required • Instant results\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_c = HowItWorksSection;\nvar _c;\n$RefreshReg$(_c, \"HowItWorksSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HowItWorksSection.tsx\n"));

/***/ })

});