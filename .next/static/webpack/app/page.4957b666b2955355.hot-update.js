"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PlatformComparisonSection.tsx":
/*!******************************************************!*\
  !*** ./src/components/PlatformComparisonSection.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlatformComparisonSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst platforms = [\n    {\n        id: \"meliuz\",\n        name: \"Meliuz\",\n        logo: \"/logos/meliuz.svg\",\n        rating: 4.7,\n        totalStores: 2800,\n        averageCashback: 4.1,\n        minPayout: 20.00,\n        payoutTime: \"2-3 days\",\n        payoutMethods: [\n            \"PIX\",\n            \"Bank Transfer\",\n            \"Gift Cards\"\n        ],\n        signupBonus: 15,\n        referralBonus: 20,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Excellent\"\n        },\n        pros: [\n            \"Leading Brazilian platform\",\n            \"PIX payments\",\n            \"High cashback rates\"\n        ],\n        cons: [\n            \"Brazil-focused stores\",\n            \"Higher minimum payout\"\n        ],\n        color: \"from-purple-500 to-indigo-500\"\n    },\n    {\n        id: \"intershopping\",\n        name: \"Inter Shopping\",\n        logo: \"/logos/intershopping.svg\",\n        rating: 4.6,\n        totalStores: 1800,\n        averageCashback: 3.5,\n        minPayout: 10.00,\n        payoutTime: \"1-2 days\",\n        payoutMethods: [\n            \"PIX\",\n            \"Inter Account\",\n            \"Bank Transfer\"\n        ],\n        signupBonus: 10,\n        referralBonus: 15,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Good\"\n        },\n        pros: [\n            \"Instant PIX payments\",\n            \"Low minimum payout\",\n            \"Bank integration\"\n        ],\n        cons: [\n            \"Smaller store selection\",\n            \"Brazil-only focus\"\n        ],\n        color: \"from-emerald-500 to-green-500\"\n    },\n    {\n        id: \"honey\",\n        name: \"Honey\",\n        logo: \"/logos/honey.svg\",\n        rating: 4.5,\n        totalStores: 2800,\n        averageCashback: 2.1,\n        minPayout: 20,\n        payoutTime: \"5-7 days\",\n        payoutMethods: [\n            \"PayPal\",\n            \"Gift Cards\"\n        ],\n        signupBonus: 0,\n        referralBonus: 5,\n        features: {\n            browserExtension: true,\n            mobileApp: true,\n            priceComparison: true,\n            couponCodes: true,\n            inStoreOffers: false,\n            customerSupport: \"Good\"\n        },\n        pros: [\n            \"Automatic coupon finding\",\n            \"Easy to use\",\n            \"Price tracking\"\n        ],\n        cons: [\n            \"Lower cashback rates\",\n            \"Higher minimum payout\"\n        ],\n        color: \"from-orange-500 to-yellow-500\"\n    },\n    {\n        id: \"befrugal\",\n        name: \"BeFrugal\",\n        logo: \"/logos/befrugal.svg\",\n        rating: 4.3,\n        totalStores: 2200,\n        averageCashback: 2.8,\n        minPayout: 25,\n        payoutTime: \"4-6 days\",\n        payoutMethods: [\n            \"PayPal\",\n            \"Check\",\n            \"Amazon Gift Card\"\n        ],\n        signupBonus: 10,\n        referralBonus: 10,\n        features: {\n            browserExtension: true,\n            mobileApp: false,\n            priceComparison: false,\n            couponCodes: true,\n            inStoreOffers: true,\n            customerSupport: \"Average\"\n        },\n        pros: [\n            \"Good signup bonus\",\n            \"In-store offers\",\n            \"Competitive rates\"\n        ],\n        cons: [\n            \"No mobile app\",\n            \"Smaller store selection\"\n        ],\n        color: \"from-blue-500 to-cyan-500\"\n    }\n];\nconst comparisonFeatures = [\n    {\n        name: \"Browser Extension\",\n        key: \"browserExtension\"\n    },\n    {\n        name: \"Mobile App\",\n        key: \"mobileApp\"\n    },\n    {\n        name: \"Price Comparison\",\n        key: \"priceComparison\"\n    },\n    {\n        name: \"Coupon Codes\",\n        key: \"couponCodes\"\n    },\n    {\n        name: \"In-Store Offers\",\n        key: \"inStoreOffers\"\n    }\n];\nfunction PlatformComparisonSection() {\n    const renderStars = (rating)=>{\n        const stars = [];\n        const fullStars = Math.floor(rating);\n        const hasHalfStar = rating % 1 !== 0;\n        for(let i = 0; i < fullStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-4 h-4 text-yellow-400\"\n            }, i, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this));\n        }\n        if (hasHalfStar) {\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-4 h-4 text-yellow-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"half\", true, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this));\n        }\n        const remainingStars = 5 - Math.ceil(rating);\n        for(let i = 0; i < remainingStars; i++){\n            stars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4 text-gray-300\"\n            }, \"empty-\".concat(i), false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this));\n        }\n        return stars;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"platforms\",\n        className: \"py-responsive bg-gradient-to-br from-gray-50 to-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-responsive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white text-gray-700 font-medium text-sm mb-6 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                \"Platform Comparison\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Compare All\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Cashback Platforms\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-responsive-sm text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Not all cashback platforms are created equal. See how they stack up across key features, rates, and user experience.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16\",\n                    children: platforms.map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br \".concat(platform.color, \" shadow-lg mb-4\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: platform.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-poppins font-bold text-gray-900 mb-2\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-1 mb-1\",\n                                            children: renderStars(platform.rating)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 font-medium\",\n                                            children: [\n                                                platform.rating,\n                                                \" rating\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Stores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        platform.totalStores.toLocaleString(),\n                                                        \"+\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Avg. Cashback\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-emerald-600\",\n                                                    children: [\n                                                        platform.averageCashback,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Min. Payout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: [\n                                                        \"$\",\n                                                        platform.minPayout\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Payout Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: platform.payoutTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: comparisonFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: feature.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        platform.features[feature.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 text-emerald-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, feature.key, true, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                (platform.signupBonus > 0 || platform.referralBonus > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 rounded-xl p-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-emerald-700 mb-2\",\n                                            children: \"Bonuses\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        platform.signupBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600 mb-1\",\n                                            children: [\n                                                \"$\",\n                                                platform.signupBonus,\n                                                \" signup bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, this),\n                                        platform.referralBonus > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-emerald-600\",\n                                            children: [\n                                                \"$\",\n                                                platform.referralBonus,\n                                                \" referral bonus\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full btn-primary justify-center group-hover:shadow-green-strong transition-all duration-200\",\n                                    children: \"Compare Rates\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, platform.id, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center bg-white rounded-3xl p-8 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-poppins font-bold text-gray-900 mb-4\",\n                            children: \"Why Choose When You Can Compare?\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                            children: \"Instead of signing up for multiple platforms, use CashBoost to instantly see which one offers the best rate for each store.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4\",\n                            children: \"Start Comparing All Platforms\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/PlatformComparisonSection.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c = PlatformComparisonSection;\nvar _c;\n$RefreshReg$(_c, \"PlatformComparisonSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PlatformComparisonSection.tsx\n"));

/***/ })

});