"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeroSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _FloatingCashbackCards__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FloatingCashbackCards */ \"(app-pages-browser)/./src/components/FloatingCashbackCards.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst trustStats = [\n    {\n        value: \"2M+\",\n        label: \"Active Users\",\n        icon: _barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        value: \"500+\",\n        label: \"Partner Stores\",\n        icon: _barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        value: \"$50M+\",\n        label: \"Cashback Earned\",\n        icon: _barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    }\n];\nfunction HeroSection() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        // Handle search logic here\n        console.log(\"Searching for:\", searchQuery);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 hero-gradient\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-20 w-40 h-40 bg-white rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FloatingCashbackCards__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container-responsive text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            className: \"inline-flex items-center px-4 py-2 rounded-full glass-green text-emerald-700 font-medium text-sm mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                \"Compare. Save. Earn More.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"text-responsive-3xl font-poppins font-black text-white mb-6 leading-tight\",\n                            children: [\n                                \"Find the\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-100 bg-clip-text text-transparent\",\n                                            children: \"Best Cashback Rates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-300 to-yellow-100 rounded-full\",\n                                            initial: {\n                                                scaleX: 0\n                                            },\n                                            animate: {\n                                                scaleX: 1\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 1\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                \"Across All Platforms\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"text-responsive-lg text-emerald-50 mb-12 max-w-2xl mx-auto leading-relaxed\",\n                            children: \"Stop leaving money on the table. Compare real-time cashback rates from top platforms and maximize your savings on every purchase.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            className: \"max-w-2xl mx-auto mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSearch,\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                placeholder: \"Search for stores like Amazon, Nike, Target...\",\n                                                className: \"w-full pl-12 pr-40 py-4 text-lg rounded-2xl border-0 shadow-green-strong focus:ring-4 focus:ring-white/20 focus:shadow-green-strong bg-white/95 backdrop-blur-sm placeholder-gray-500 text-gray-900 outline-none transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 bg-emerald-500 hover:bg-emerald-600 text-white font-semibold px-6 py-2.5 rounded-xl transition-all duration-200 shadow-md hover:shadow-lg\",\n                                                children: \"Compare Rates\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium text-sm\",\n                                                children: \"Popular stores:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            [\n                                                \"Amazon\",\n                                                \"Nike\",\n                                                \"Target\",\n                                                \"Best Buy\",\n                                                \"Walmart\"\n                                            ].map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSearchQuery(store),\n                                                    className: \"px-4 py-2 text-sm text-white bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/50 rounded-full transition-all duration-200 focus-visible font-medium\",\n                                                    children: store\n                                                }, store, false, {\n                                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.8\n                            },\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto\",\n                            children: trustStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 1 + index * 0.1\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-12 h-12 rounded-xl glass-green mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-6 h-6 text-emerald-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl md:text-3xl font-poppins font-bold text-white mb-1\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-emerald-100 text-sm font-medium\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, stat.label, true, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1.2\n                            },\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-primary text-lg px-8 py-4 animate-pulse-green\",\n                                    children: \"Start Comparing Now\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-8 py-4\",\n                                    children: \"How It Works\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1,\n                    delay: 1.5\n                },\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    animate: {\n                        y: [\n                            0,\n                            10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    className: \"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        animate: {\n                            y: [\n                                0,\n                                12,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        },\n                        className: \"w-1 h-3 bg-white/60 rounded-full mt-2\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroSection, \"uixqA8hxOTN7LqZPWxVzG2fnyhQ=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HeroSection.tsx\n"));

/***/ })

});